<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thinkgem.jeesite.modules.bas.dao.ProductDao">

	<sql id="productColumns">
		a.id AS "id",
		a.name AS "name",
		a.user_id AS "userId",
		a.ent_id AS "entId",
		a.data_scope AS "dataScope",
		a.product_sort_code AS "productSortCode",
		a.product_sort_name AS "productSortName",
		a.product_certification_code AS "productCertificationCode",
		a.product_certification_name AS "productCertificationName",
		a.add_date AS "addDate",
		a.province AS "province",
		a.city AS "city",
		a.county AS "county",
		a.address AS "address",
		a.detail AS "detail",
		a.longitude AS "longitude",
		a.latitude AS "latitude",
		a.product_introduction AS "productIntroduction",
		a.current_sample_no AS "currentSampleNo",
		a.query_code_url AS "queryCodeUrl",
		a.electricity_link AS "electricityLink",
		a.scale_amount AS "scaleAmount",
		a.scale_unit_code AS "scaleUnitCode",
		a.scale_unit_name AS "scaleUnitName",
		a.production_value AS "productionValue",
		a.production_amount AS "productionAmount",
		a.production_unit_code AS "productionUnitCode",
		a.production_unit_name AS "productionUnitName",
		a.print_amount AS "printAmount",
		a.print_date AS "printDate",
		a.re_buy_product_id AS "reBuyProductId",
		a.re_buy_visible AS "reBuyVisible",
		a.save_type as "saveType",
		a.re_buy_product_name AS "reBuyProductName",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.update_by AS "updateBy.id",
		a.update_date AS "updateDate",
		a.remarks AS "remarks",
		a.del_flag AS "delFlag",
		a.sync_date as "syncDate"
	</sql>

	<sql id="productJoins">
	</sql>

	<select id="get" resultType="Product">
		SELECT
			<include refid="productColumns"/>
		FROM bas_product a
		<include refid="productJoins"/>
		WHERE a.id = #{id}
	</select>
	<select id="findList" resultType="Product">
		SELECT
			<include refid="productColumns"/>
		FROM bas_product a
		<include refid="productJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			<if test="name != null and name != ''">
				AND a.name LIKE
					<if test="dbName == 'oracle'">'%'||#{name}||'%'</if>
					<if test="dbName == 'mssql'">'%'+#{name}+'%'</if>
					<if test="dbName == 'mysql'">concat('%',#{name},'%')</if>
			</if>
			<if test="productSortCode != null and productSortCode != ''">
				AND a.product_sort_code = #{productSortCode}
			</if>
			<if test="entId != null and entId != ''">
				AND a.ent_id = #{entId}
			</if>
			<if test="userId != null and userId != ''">
				AND a.user_id = #{userId}
			</if>
			<if test="name != null and name != ''">
				and a.name like concat('%',#{name},'%')
			</if>
			<if test="dataScope != null and dataScope != ''">
				and a.data_scope = #{dataScope}
			</if>
		<!-- 	<if test="beginAddDate != null and endAddDate != null and beginAddDate != '' and endAddDate != ''">
				AND a.add_date BETWEEN #{beginAddDate} AND #{endAddDate}
			</if> -->
			<if test="beginAddDate != null">
				AND Date(a.add_date) &gt;= Date(#{beginAddDate})
			</if>
			<if test="endAddDate != null">
				AND Date(a.add_date) &lt;= Date(#{endAddDate})
			</if>

			<if test="beginPrintDate != null">
                AND Date(a.print_date) <![CDATA[ >= ]]>  Date(#{beginPrintDate})
            </if>
            <if test="endPrintDate != null">
                AND Date(a.print_date)<![CDATA[ <= ]]> Date(#{endPrintDate})
            </if>
			<if test="saveType != null">
				and a.save_type = #{saveType}
			</if>
			<if test="syncDate != null">
				AND Date(a.update_date) <![CDATA[ >= ]]> Date(#{syncDate})
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>

	<select id="findAllList" resultType="Product">
		SELECT
			<include refid="productColumns"/>
		FROM bas_product a
		<include refid="productJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			<if test="syncDate != null">
				AND Date(a.update_date) <![CDATA[ >= ]]> Date(#{syncDate})
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>

	<insert id="insert">
		INSERT INTO bas_product(
			id,
			name,
			user_id,
			ent_id,
			data_scope,
			product_sort_code,
			product_sort_name,
			product_certification_code,
			product_certification_name,
			add_date,
			province,
			city,
			county,
			address,
			detail,
			longitude,
			latitude,
			product_introduction,
		    current_sample_no,
		    query_code_url,
			electricity_link,
			scale_amount,
			scale_unit_code,
			scale_unit_name,
			production_value,
			production_amount,
			production_unit_code,
			production_unit_name,
			print_amount,
			priint_date,
			re_buy_product_id,
			re_buy_visible,
			save_type,
			re_buy_product_name,
			create_by,
			create_date,
			update_by,
			update_date,
			remarks,
			del_flag,
		    sync_date
		) VALUES (
			#{id},
			#{name},
			#{userId},
			#{entId},
			#{dataScope},
			#{productSortCode},
			#{productSortName},
			#{productCertificationCode},
			#{productCertificationName},
			#{addDate},
			#{province},
			#{city},
			#{county},
			#{address},
			#{detail},
			#{longitude},
			#{latitude},
			#{productIntroduction},
			#{currentSampleNo},
			#{queryCodeUrl},
			#{electricityLink},
			#{scaleAmount},
			#{scaleUnitCode},
			#{scaleUnitName},
			#{productionValue},
			#{productionAmount},
			#{productionUnitCode},
			#{productionUnitName},
			#{printAmount},
			#{priintDate},
			#{reBuyProductId},
			#{reBuyVisible},
			#{saveType},
			#{reBuyProductName},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate},
			#{remarks},
			#{delFlag},
		    #{syncDate}
		)
	</insert>

	<update id="update">
		UPDATE bas_product SET
			name = #{name},
			user_id = #{userId},
			ent_id = #{entId},
			data_scope = #{dataScope},
			product_sort_code = #{productSortCode},
			product_sort_name = #{productSortName},
			product_certification_code = #{productCertificationCode},
			product_certification_name = #{productCertificationName},
			add_date = #{addDate},
			province = #{province},
			city = #{city},
			county = #{county},
			address = #{address},
			detail = #{detail},
			longitude = #{longitude},
			latitude = #{latitude},
			product_introduction = #{productIntroduction},
			current_sample_no = #{currentSampleNo},
			query_code_url = #{queryCodeUrl},
			electricity_link = #{electricityLink},
			scale_amount = #{scaleAmount},
			scale_unit_code = #{scaleUnitCode},
			scale_unit_name = #{scaleUnitName},
			production_value = #{productionValue},
			production_amount = #{productionAmount},
			production_unit_code = #{productionUnitCode},
			production_unit_name = #{productionUnitName},
			print_amount = #{printAmount},
			print_date = #{printDate},
			re_buy_product_id = #{reBuyProductId},
			re_buy_visible  = #{reBuyVisible},
			save_type = #{saveType},
			re_buy_product_name = #{reBuyProductName},
			update_by = #{updateBy.id},
			update_date = #{updateDate},
			remarks = #{remarks},
			sync_date = #{syncDate}
		WHERE id = #{id}
	</update>

	<update id="delete">
		UPDATE bas_product SET
			del_flag = #{DEL_FLAG_DELETE}
		WHERE id = #{id}
	</update>

	<select id="findProductList" resultType="com.thinkgem.jeesite.modules.bas.entity.Product">
		SELECT
		<include refid="productColumns"/>
		FROM bas_product a
		<include refid="productJoins"/>
		WHERE a.ent_id = #{entId}
		and a.del_flag = 0
		<if test="syncDate != null">
			AND Date(a.update_date) <![CDATA[ >= ]]> Date(#{syncDate})
		</if>
	</select>

	<select id="findProductPage" resultType="com.thinkgem.jeesite.modules.bas.entity.Product">
		SELECT
		<include refid="productColumns"/>
		, sum(b.print_count) AS "count"
		FROM bas_product a
		LEFT JOIN bas_certificate b ON a.id = b.product_id and b.del_flag = #{DEL_FLAG_NORMAL}
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL} and a.save_type != '1'
			<if test="name != null and name != ''">
				AND a.name LIKE
				<if test="dbName == 'oracle'">'%'||#{name}||'%'</if>
				<if test="dbName == 'mssql'">'%'+#{name}+'%'</if>
				<if test="dbName == 'mysql'">concat('%',#{name},'%')</if>
			</if>
			<if test="productSortCode != null and productSortCode != ''">
				AND a.product_sort_code = #{productSortCode}
			</if>
			<if test="entId != null and entId != ''">
				AND a.ent_id = #{entId}
			</if>
			<!-- <if test="beginAddDate != null and endAddDate != null and beginAddDate != '' and endAddDate != ''">
				AND a.add_date BETWEEN #{beginAddDate} AND #{endAddDate}
			</if> -->
			<if test="beginAddDate != null">
				AND Date(a.add_date) &gt;= Date(#{beginAddDate})
			</if>
			<if test="endAddDate != null">
				AND Date(a.add_date) &lt;= Date(#{endAddDate})
			</if>
		    <if test="saveType != null">
				and a.save_type = #{saveType}
			</if>
			<if test="syncDate != null">
				AND Date(a.update_date) <![CDATA[ >= ]]> Date(#{syncDate})
			</if>
		</where>
		group by  a.id
		<choose>
			<when test="addDateOrder == 1 ">
				ORDER BY a.add_date asc
			</when>
			<when test="addDateOrder == 2">
				ORDER BY a.add_date desc
			</when>
			<when test="printCountOrder == 1">
				ORDER BY b.print_count+0 asc
			</when>
			<when test="printCountOrder == 2">
				ORDER BY b.print_count+0  desc
			</when>
			<otherwise>
				ORDER BY a.update_date,a.id DESC
			</otherwise>
		</choose>
	</select>

	<delete id="deleteBatch" parameterType="string">
		UPDATE bas_product SET
		del_flag = 1
		WHERE id in
		<foreach collection="list" item="item" open="(" separator=","
				 close=")">
			#{item}
		</foreach>
	</delete>
	<update id="updateInspectionResultById">
<!-- 	UPDATE bas_product SET
			current_sample_no = #{sampleNo},
			query_code_url = #{queryCodeUrl}
		WHERE id = #{productId} -->
		UPDATE bas_product bp
		INNER JOIN
			<foreach collection="list" item="item" open="(" separator=" UNION ALL " close=")">
				SELECT #{item.sampleNo} AS "sampleNo" ,#{item.sampleId} AS "productId", #{item.qrcodeUrl} AS "queryCodeUrl"
			</foreach> a
		ON bp.id =a.productId
		SET bp.current_sample_no = a.sampleNo,bp.query_code_url =a.queryCodeUrl
	</update>

	<select id="findProductAllList" resultType="com.thinkgem.jeesite.modules.bas.entity.Product">
		SELECT
		<include refid="productColumns"/>
		FROM bas_product a
		<where>
			a.del_flag = '0'
			<if test="name != null and name != ''">
				and a.name like concat('%',#{name},'%')
			</if>
			<if test="syncDate != null">
				AND Date(a.update_date) <![CDATA[ >= ]]> Date(#{syncDate})
			</if>
		</where>
	</select>
	<select id="findCountGroupName" resultType="map">
		SELECT
			COUNT(DISTINCT a.name) count
		FROM bas_product a
		WHERE
			a.del_flag = '0' and a.data_scope='1'
		<if test="name != null and name != ''">
			and a.name like concat('%',#{name},'%')
		</if>
		<if test="userId != null and userId != ''">
		AND a.user_id = #{userId}
	    </if>
	</select>
	<select id="findProductById" resultType="com.thinkgem.jeesite.modules.bas.entity.Product" parameterType="string">
		SELECT
		<include refid="productColumns"/>
		FROM bas_product a
		WHERE
			a.del_flag = '0' and a.id=#{id}
	</select>
	<update id="updateById">
		UPDATE bas_product SET
			name = #{name},
			user_id = #{userId},
			ent_id = #{entId},
			data_scope = #{dataScope},
			product_sort_code = #{productSortCode},
			product_sort_name = #{productSortName},
			product_certification_code = #{productCertificationCode},
			product_certification_name = #{productCertificationName},
			add_date = #{addDate},
			province = #{province},
			city = #{city},
			county = #{county},
			address = #{address},
			detail = #{detail},
			longitude = #{longitude},
			latitude = #{latitude},
			product_introduction = #{productIntroduction},
			current_sample_no = #{currentSampleNo},
			query_code_url = #{queryCodeUrl},
			electricity_link = #{electricityLink},
			scale_amount = #{scaleAmount},
			scale_unit_code = #{scaleUnitCode},
			scale_unit_name = #{scaleUnitName},
			production_value = #{productionValue},
			production_amount = #{productionAmount},
			production_unit_code = #{productionUnitCode},
			production_unit_name = #{productionUnitName},
			print_amount = #{printAmount},
			print_date = #{printDate},
			re_buy_product_id = #{reBuyProductId},
			re_buy_visible  = #{reBuyVisible},
			save_type = #{saveType},
			re_buy_product_name = #{reBuyProductName},
			update_by = #{updateBy.id},
			update_date = #{updateDate},
			remarks = #{remarks},
			sync_date = #{syncDate}
		WHERE id = #{id}
	</update>
	<update id="deleteFlag">
		UPDATE bas_product SET
			del_flag = '1'
		WHERE id = #{id}
	</update>
	<select id="findProductCount" resultType="map">
		SELECT COUNT(1) productCount FROM bas_product WHERE `name` = #{name} and del_flag = '0' and data_scope ='1'
		<if test="userId != null and userId != ''">
			AND  user_id = #{userId}
		</if>
	</select>

    <select id="findListByUserId" resultType="com.thinkgem.jeesite.modules.bas.entity.Product">
        SELECT
            bp.id,
            bp.name,
        IFNULL(a.count,0) AS count
        FROM
            bas_product bp
        LEFT JOIN (
            SELECT
                IFNULL(COUNT(c.product_id),0) AS count,
                c.product_id,
                c.product_name
            FROM
                bas_certificate c
            WHERE
                c.del_flag = 0
            <if test="userId != null and userId != ''">
                AND c.user_id = #{userId}
            </if>
            GROUP BY
                c.product_id
            ORDER BY
                count DESC
        ) a ON a.product_id = bp.id
        WHERE
            bp.del_flag = '0'
        <if test="userId != null and userId != ''">
            AND  bp.user_id = #{userId}
        </if>
        <if test="name != null and name != ''">
            AND bp.name like concat('%',#{name},'%')
        </if>
        ORDER BY count DESC,bp.add_date ASC
    </select>

    <update id="updatePrintAmount">
        UPDATE bas_product SET
            print_amount = print_amount+#{amount},
            print_date = now()
        WHERE id = #{id}
    </update>

    <select id="findListPage" resultType="Product">
        SELECT
            <include refid="productColumns"/>
        FROM bas_product a
        <include refid="productJoins"/>
        <where>
            a.del_flag = #{DEL_FLAG_NORMAL}
            <if test="name != null and name != ''">
                AND a.name LIKE
                    <if test="dbName == 'oracle'">'%'||#{name}||'%'</if>
                    <if test="dbName == 'mssql'">'%'+#{name}+'%'</if>
                    <if test="dbName == 'mysql'">concat('%',#{name},'%')</if>
            </if>
            <if test="productSortCode != null and productSortCode != ''">
                AND a.product_sort_code = #{productSortCode}
            </if>
            <if test="entId != null and entId != ''">
                AND a.ent_id = #{entId}
            </if>
            <if test="userId != null and userId != ''">
                AND a.user_id = #{userId}
            </if>
            <if test="name != null and name != ''">
                and a.name like concat('%',#{name},'%')
            </if>
            <if test="dataScope != null and dataScope != ''">
                and a.data_scope = #{dataScope}
            </if>
            <if test="beginAddDate != null">
                AND Date(a.add_date) &gt;= Date(#{beginAddDate})
            </if>
            <if test="endAddDate != null">
                AND Date(a.add_date) &lt;= Date(#{endAddDate})
            </if>

            <if test="beginPrintDate != null">
                AND Date(a.print_date) <![CDATA[ >= ]]>  Date(#{beginPrintDate})
            </if>
            <if test="endPrintDate != null">
                AND Date(a.print_date)<![CDATA[ <= ]]> Date(#{endPrintDate})
            </if>
        	<if test="saveType != null">
				and a.save_type = #{saveType}
			</if>
        </where>
        <choose>
            <when test="page !=null and page.orderBy != null and page.orderBy == 'printDate'">
                ORDER BY a.print_date DESC
            </when>
            <otherwise>
                ORDER BY a.update_date DESC
            </otherwise>
        </choose>
    </select>

	<update id="updateInvalidPrintAmount">
		UPDATE bas_product t
			INNER JOIN (
			SELECT
			c.ent_id AS entId,
			c.product_id as product_id,
			SUM(IF(c.id!="" and c.del_flag = '0',1,0)) certificateAmount,
			SUM(IF(c.print_count!="" and c.del_flag = '0',c.print_count,0)) certificatePrintAmount,
			max(IF(c.del_flag = '0',c.create_date,null)) createDate
			FROM
			bas_certificate c
			where c.ent_id = #{entId}
		    and c.product_id=#{productId}
			group by c.ent_id
			) a ON t.id = a.product_id and t.ent_id = a.entId
			SET t.print_amount = a.certificatePrintAmount,
				t.print_date = a.createDate
		where t.id = #{productId}
	</update>
</mapper>
