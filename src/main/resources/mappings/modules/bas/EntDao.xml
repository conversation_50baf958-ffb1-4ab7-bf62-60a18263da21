<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thinkgem.jeesite.modules.bas.dao.EntDao">

	<sql id="entColumns">
		a.id AS "id",
		a.name AS "name",
		a.business_type AS "businessType",
		a.ent_type AS "entType",
		a.main_type AS "mainType",
		a.farm_type AS "farmType",
		a.social_code AS "socialCode",
		a.card_no AS "cardNo",
		a.card_detail AS "cardDetail",
		a.legal_person AS "legalPerson",
		a.contacts AS "contacts",
		a.contacts_phone AS "contactsPhone",
		a.province AS "province",
		a.city AS "city",
		a.county AS "county",
		a.town AS "town",
		a.village AS "village",
		a.address As "address",
		a.detail AS "detail",
		a.lng AS "lng",
		a.lat AS "lat",
		a.company_introduction AS "companyIntroduction",
		a.ent_honor AS "entHonor",
		a.submit_date AS "submitDate",
		a.examine_status AS "examineStatus",
		a.examine_man AS "examineMan",
		a.examine_opinion AS "examineOpinion",
		a.examine_date AS "examineDate",
		a.table_name AS "tableName",
		a.table_id AS "tableId",
		a.basic_flag AS "basicFlag",
		a.basic_enter_flag AS "basicEnterFlag",
		a.certificate_amount AS "certificateAmount",
		a.certificate_print_amount AS "certificatePrintAmount",
		a.certificate_print_date AS "certificatePrintDate",
		a.inspection_write_amount AS "inspectionWriteAmount",
		a.change_status AS "changeStatus",
		a.change_view_flag AS "changeViewFlag",
		a.change_opinion AS "changeOpinion",
		a.frozen_flag AS "frozenFlag",
		a.user_id AS "userId",
		a.data_scope AS "dataScope",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.update_by AS "updateBy.id",
		a.update_date AS "updateDate",
		a.remarks AS "remarks",
		a.del_flag AS "delFlag",
		a.last_modified as "lastModified",
		a.sync_date as "syncDate"
	</sql>

	<sql id="entJoins">
	      left join bas_autograph ba on ba.ent_id=a.id and ba.del_flag=0
	</sql>
    <sql id="productJoins">
          left join bas_product p on p.ent_id=a.id and p.del_flag=0
    </sql>
	<select id="get" resultType="Ent">
		SELECT
			<include refid="entColumns"/>
		FROM bas_ent a
		WHERE a.id = #{id}
	</select>

	<select id="getEntByName" resultType="Ent">
		SELECT
		<include refid="entColumns"/>
		FROM bas_ent a
		WHERE a.name = #{name}
		AND a.del_flag = #{DEL_FLAG_NORMAL}
		limit 1
	</select>

	<select id="getEnt" resultType="Ent">
		SELECT
		<include refid="entColumns"/>
		FROM bas_ent a
		WHERE a.table_id = #{tableId}
		AND a.del_flag = #{DEL_FLAG_NORMAL}
	</select>

	<select id="findList" resultType="Ent">
		SELECT
			<include refid="entColumns"/>
			,GROUP_CONCAT(DISTINCT p.product_sort_name) as productSortName
		FROM bas_ent a
		<include refid="entJoins"/>
		<include refid="productJoins"/>
		<where>
            <if test="userId != null and userId != ''">
                and a.user_id = #{userId}
            </if>
            <if test="dataScope != null and dataScope != ''">
                and a.data_scope = #{dataScope}
            </if>
			<if test="entType !=null and entType !=''">
				AND a.ent_type = #{entType}
			</if>
		    <if test="name !=null and name !=''">
		    	AND a.name LIKE concat('%',#{name},'%')
		    </if>
		    <if test="businessType !=null and businessType !=''">
		    	AND a.business_type = #{businessType}
		    </if>
		    <if test="county !=null and county !=''">
		    	AND a.county LIKE concat(#{county},'%')
		    </if>
			<if test="sqlMap.areaWhere!='' and sqlMap.areaWhere!=null">
				${sqlMap.areaWhere}
			</if>
		    <if test="examineStatus !=null and examineStatus !=''">
		    	AND a.examine_status = #{examineStatus}
		    </if>
			<if test="cardNo !=null and cardNo !=''">
				AND a.card_no = #{cardNo}
			</if>
			<!-- 审核 -->
			<if test="remarks !=null and remarks !=''">
				AND examine_status	&lt; #{remarks}
			</if>
			<if test="startDate !=null">
				AND a.submit_date &gt;= #{startDate}
			</if>
			<if test="endDate !=null">
				AND a.submit_date &lt;= #{endDate}
			</if>
			<if test="frozenFlag !=null and frozenFlag !=''">
                AND a.frozen_flag = #{frozenFlag}
            </if>
            <if test="productSortName !=null and productSortName !=''">
                AND p.product_sort_name = #{productSortName}
            </if>
            <if test="beginCreateDate != null">
                AND Date(a.create_date) <![CDATA[ >= ]]>  Date(#{beginCreateDate})
            </if>
            <if test="endPrintDate != null">
                AND Date(a.create_date)<![CDATA[ <= ]]> Date(#{endPrintDate})
            </if>
            <if test="beginCertificatePrintDate != null">
                AND Date(a.certificate_print_date) <![CDATA[ >= ]]>  Date(#{beginCertificatePrintDate})
            </if>
            <if test="endCertificatePrintDate != null">
                AND Date(a.certificate_print_date)<![CDATA[ <= ]]> Date(#{endCertificatePrintDate})
            </if>
			<if test="syncDate != null">
				AND Date(a.update_date) <![CDATA[ >= ]]> Date(#{syncDate})
			</if>
			AND a.del_flag = #{DEL_FLAG_NORMAL}
		</where>
		  group by a.id
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
			    <choose>
			        <when test="page.orderBy == 'certificate_amount ASC'">
                        ORDER BY a.certificate_amount ASC
                    </when>
                    <when test="page.orderBy == 'certificate_amount DESC'">
                        ORDER BY a.certificate_amount DESC
                    </when>
                    <when test="page.orderBy == 'certificate_print_amount ASC'">
                        ORDER BY a.certificate_print_amount ASC
                    </when>
                    <when test="page.orderBy == 'certificate_print_amount DESC'">
                        ORDER BY a.certificate_print_amount DESC
                    </when>
                    <when test="page.orderBy == 'certificate_print_date ASC'">
                        ORDER BY a.certificate_print_date ASC
                    </when>
                    <when test="page.orderBy == 'certificate_print_date DESC'">
                        ORDER BY a.certificate_print_date DESC
                    </when>
			        <when test="page.orderBy == 'certificateAmount'">
			            ORDER BY a.certificate_print_amount desc,certificate_amount desc,a.update_date desc
			        </when>
			    </choose>
			</when>

			<otherwise>
				<choose>
					<when test="exportFlag == 'export'">
						ORDER BY a.certificate_print_amount desc,certificate_amount desc,a.update_date desc
					</when>
					<otherwise>
						ORDER BY FIELD(a.examine_status,0,-1,1),a.update_date DESC
					</otherwise>
				</choose>
			</otherwise>
		</choose>
	</select>

	<select id="findListQuick" resultType="Ent">
		SELECT
		a.id AS "id",
		a.name AS "name",
		a.business_type AS "businessType",
		a.main_type AS "mainType",
		a.legal_person AS "legalPerson",
		a.province AS "province",
		a.city AS "city",
		a.county AS "county",
		a.address As "address",
		a.detail AS "detail",
		a.create_date AS "createDate",
		a.certificate_amount AS "certificateAmount",
		a.certificate_print_amount AS "certificatePrintAmount",
		a.inspection_write_amount AS "inspectionWriteAmount",
		a.ent_type AS "entType",
		a.certificate_print_date AS "certificatePrintDate",
		a.card_no AS "cardNo",
		a.contacts AS "contacts",
		a.contacts_phone AS "contactsPhone",
		a.social_code AS "socialCode",
		GROUP_CONCAT(DISTINCT p.product_sort_name) as productSortName
		FROM bas_ent a
		<include refid="productJoins"/>
		<where>
			<if test="userId != null and userId != ''">
				and a.user_id = #{userId}
			</if>
			<if test="dataScope != null and dataScope != ''">
				and a.data_scope = #{dataScope}
			</if>
			<if test="entType !=null and entType !=''">
				AND a.ent_type = #{entType}
			</if>
			<if test="name !=null and name !=''">
				AND a.name LIKE concat('%',#{name},'%')
			</if>
			<if test="businessType !=null and businessType !=''">
				AND a.business_type = #{businessType}
			</if>
			<if test="county !=null and county !=''">
				AND a.county LIKE concat(#{county},'%')
			</if>
			<if test="sqlMap.areaWhere!='' and sqlMap.areaWhere!=null">
				${sqlMap.areaWhere}
			</if>
			<if test="examineStatus !=null and examineStatus !=''">
				AND a.examine_status = #{examineStatus}
			</if>
			<if test="cardNo !=null and cardNo !=''">
				AND a.card_no = #{cardNo}
			</if>
			<!-- 审核 -->
			<if test="remarks !=null and remarks !=''">
				AND examine_status	&lt; #{remarks}
			</if>
			<if test="startDate !=null">
				AND a.submit_date &gt;= #{startDate}
			</if>
			<if test="endDate !=null">
				AND a.submit_date &lt;= #{endDate}
			</if>
			<if test="frozenFlag !=null and frozenFlag !=''">
				AND a.frozen_flag = #{frozenFlag}
			</if>
			<if test="productSortName !=null and productSortName !=''">
				AND p.product_sort_name = #{productSortName}
			</if>
			<if test="beginCreateDate != null">
				AND Date(a.create_date) <![CDATA[ >= ]]>  Date(#{beginCreateDate})
			</if>
			<if test="endPrintDate != null">
				AND Date(a.create_date)<![CDATA[ <= ]]> Date(#{endPrintDate})
			</if>
			<if test="beginCertificatePrintDate != null">
				AND Date(a.certificate_print_date) <![CDATA[ >= ]]>  Date(#{beginCertificatePrintDate})
			</if>
			<if test="endCertificatePrintDate != null">
				AND Date(a.certificate_print_date)<![CDATA[ <= ]]> Date(#{endCertificatePrintDate})
			</if>
			<if test="syncDate != null">
				AND Date(a.update_date) <![CDATA[ >= ]]> Date(#{syncDate})
			</if>
			AND a.del_flag = #{DEL_FLAG_NORMAL}
		</where>
		group by a.id
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				<choose>
					<when test="page.orderBy == 'certificate_amount ASC'">
						ORDER BY a.certificate_amount ASC
					</when>
					<when test="page.orderBy == 'certificate_amount DESC'">
						ORDER BY a.certificate_amount DESC
					</when>
					<when test="page.orderBy == 'certificate_print_amount ASC'">
						ORDER BY a.certificate_print_amount ASC
					</when>
					<when test="page.orderBy == 'certificate_print_amount DESC'">
						ORDER BY a.certificate_print_amount DESC
					</when>
					<when test="page.orderBy == 'certificate_print_date ASC'">
						ORDER BY a.certificate_print_date ASC
					</when>
					<when test="page.orderBy == 'certificate_print_date DESC'">
						ORDER BY a.certificate_print_date DESC
					</when>
					<when test="page.orderBy == 'inspection_write_amount DESC'">
						ORDER BY a.inspection_write_amount DESC
					</when>
					<when test="page.orderBy == 'inspection_write_amount ASC'">
						ORDER BY a.inspection_write_amount ASC
					</when>
					<when test="page.orderBy == 'certificateAmount'">
						ORDER BY a.certificate_print_amount desc,certificate_amount desc,a.update_date desc
					</when>
				</choose>
			</when>

			<otherwise>
				<choose>
					<when test="exportFlag == 'export'">
						ORDER BY a.certificate_print_amount desc,certificate_amount desc,a.update_date desc
					</when>
					<otherwise>
						ORDER BY FIELD(a.examine_status,0,-1,1),a.update_date DESC
					</otherwise>
				</choose>
			</otherwise>
		</choose>
	</select>
	<select id="findAllList" resultType="Ent">
		SELECT
			<include refid="entColumns"/>
		FROM bas_ent a
		<include refid="entJoins"/>
		<where>
			<if test="socialCode !=null and socialCode !=''">
				AND a.social_code = #{socialCode}
			</if>
			<if test="cardNo !=null and socialCode !=''">
				AND a.card_no = #{cardNo}
			</if>
			<if test="syncDate != null">
				AND Date(a.update_date) <![CDATA[ >= ]]> Date(#{syncDate})
			</if>
			AND a.del_flag = #{DEL_FLAG_NORMAL}
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>

	<insert id="insert">
		INSERT INTO bas_ent(
			id,
			name,
			business_type,
			ent_type,
			main_type,
			farm_type,
			social_code,
			card_no,
			card_detail,
			legal_person,
			contacts,
			contacts_phone,
			province,
			city,
			county,
			town,
			village,
			address,
			detail,
			lng,
			lat,
			company_introduction,
			ent_honor,
			submit_date,
			examine_status,
			examine_man,
			examine_opinion,
			examine_date,
			table_name,
			table_id,
			basic_flag,
			basic_enter_flag,
			certificate_amount,
			certificate_print_amount,
			certificate_print_date,
		    inspection_write_amount,
		    change_status,
		    change_view_flag,
		    change_opinion,
		    frozen_flag,
			user_id,
			data_scope,
			create_by,
			create_date,
			update_by,
			update_date,
			remarks,
			del_flag,
			last_modified,
		    sync_date
		) VALUES (
			#{id},
			#{name},
			#{businessType},
			#{entType},
			#{mainType},
			#{farmType},
			#{socialCode},
			#{cardNo},
			#{cardDetail},
			#{legalPerson},
			#{contacts},
			#{contactsPhone},
			#{province},
			#{city},
			#{county},
			#{town},
			#{village},
			#{address},
			#{detail},
			#{lng},
			#{lat},
			#{companyIntroduction},
			#{entHonor},
			#{submitDate},
			#{examineStatus},
			#{examineMan},
			#{examineOpinion},
			#{examineDate},
			#{tableName},
			#{tableId},
			#{basicFlag},
			#{basicEnterFlag},
			#{certificateAmount},
			#{certificatePrintAmount},
			#{certificatePrintDate},
			#{inspectionWriteAmount},
			#{changeStatus},
			#{changeViewFlag},
			#{changeOpinion},
			#{frozenFlag},
			#{userId},
			#{dataScope},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate},
			#{remarks},
			#{delFlag},
			#{lastModified},
			#{syncDate}
		)
	</insert>

	<update id="update">
		UPDATE bas_ent SET
			name = #{name},
			business_type = #{businessType},
			ent_type = #{entType},
			main_type = #{mainType},
			farm_type = #{farmType},
			social_code = #{socialCode},
			card_no = #{cardNo},
			card_detail = #{cardDetail},
			legal_person = #{legalPerson},
			contacts = #{contacts},
			contacts_phone = #{contactsPhone},
			province = #{province},
			city = #{city},
			county = #{county},
			town = #{town},
			village = #{village},
			address = #{address},
			detail = #{detail},
			lng = #{lng},
			lat = #{lat},
			company_introduction = #{companyIntroduction},
			ent_honor = #{entHonor},
			submit_date = #{submitDate},
			examine_status = #{examineStatus},
			examine_man = #{examineMan},
			examine_opinion = #{examineOpinion},
			examine_date = #{examineDate},
			table_name = #{tableName},
			table_id = #{tableId},
			basic_flag = #{basicFlag},
			basic_enter_flag = #{basicEnterFlag},
			certificate_amount = #{certificateAmount},
			certificate_print_amount = #{certificatePrintAmount},
			certificate_print_date = #{certificatePrintDate},
			inspextion_write_amount = #{inspectionWriteAmount},
			change_status = #{changeStatus},
			change_view_flag = #{changeViewFlag},
			change_opinion = #{changeOpinion},
			frozen_flag = #{frozenFlag},
			user_id = #{userId},
			data_scope = #{dataScope},
			update_by = #{updateBy.id},
			update_date = #{updateDate},
			remarks = #{remarks},
			last_modified = #{lastModified},
			sync_date = #{syncDate}
		WHERE id = #{id}
	</update>

	<update id="delete">
		UPDATE bas_ent SET
			del_flag = #{DEL_FLAG_DELETE}
		WHERE id = #{id}
	</update>
	<select id="getBySocialCodeOrCardNo" resultType="int">
		SELECT count(*)
		FROM bas_ent a
		<include refid="entJoins"/>
		<where>
			<if test="socialCode !=null and socialCode !=''">
				AND a.social_code = #{socialCode}
			</if>
			<if test="cardNo !=null and socialCode !=''">
				AND a.card_no = #{cardNo}
			</if>
			AND a.id != #{id}
			AND a.del_flag = #{DEL_FLAG_NORMAL}
		</where>
	</select>
	<select id="getEntStatistic" resultType="map">
		SELECT
			COUNT( CASE WHEN a.ent_type ='0' AND a.examine_status ='1' THEN id END ) AS 'entAmount',
			COUNT( CASE WHEN a.ent_type ='1' AND a.examine_status ='1' THEN id END ) AS 'personAmount',
			COUNT( CASE WHEN a.ent_type ='0' AND a.examine_status ='0' THEN id END ) AS 'entExamineAmount',
			COUNT( CASE WHEN a.ent_type ='1' AND a.examine_status ='0' THEN id END ) AS 'personExamineAmount'
		FROM bas_ent a
		WHERE a.del_flag = '0' AND a.frozen_flag='0' and a.county LIKE concat(#{county},'%')
		<if test="sqlMap.areaWhere!='' and sqlMap.areaWhere!=null">
			${sqlMap.areaWhere}
		</if>
	</select>
	<select id="getByTableId" resultType="Ent">
        SELECT
            <include refid="entColumns"/>,
            ba.autograph
        FROM bas_ent a
            <include refid="entJoins"/>
        WHERE a.table_id = #{tableId} AND a.del_flag = '0'
        LIMIT 1
    </select>

	<select id="checkIdCard" resultType="int">
		SELECT count(*)
		FROM bas_ent a
		WHERE a.card_no = #{cardNo} and a.id != #{id} and a.del_flag = #{DEL_FLAG_NORMAL}
	</select>

	<update id="examineSave">
		UPDATE bas_ent SET
			examine_status = #{examineStatus},
			examine_man = #{examineMan},
			examine_opinion = #{examineOpinion},
			examine_date = #{examineDate},
			update_by = #{updateBy.id},
			update_date = #{updateDate}
		WHERE id = #{id}
	</update>

	<update id="updateCertificateAmount">
        UPDATE bas_ent SET
            certificate_amount = certificate_amount+1,
            certificate_print_amount = certificate_print_amount+#{amount},
            certificate_print_date = now()
        WHERE id = #{id}
    </update>

	<update id="updateCertificatePrintAmount">
		UPDATE bas_ent SET
		   certificate_print_amount = certificate_print_amount+#{amount},
		   certificate_print_date = now()
		WHERE id = #{id}
	</update>

	<update id="updateInspectionWriteAmount">
		UPDATE bas_ent SET
						   inspection_write_amount = inspection_write_amount+1
		WHERE id = #{id}
	</update>

	<update id="changeUpdate">
        UPDATE bas_ent SET
            name = #{name},
            business_type = #{businessType},
            ent_type = #{entType},
            main_type = #{mainType},
            farm_type = #{farmType},
            social_code = #{socialCode},
            card_no = #{cardNo},
            card_detail = #{cardDetail},
            legal_person = #{legalPerson},
            contacts = #{contacts},
            contacts_phone = #{contactsPhone},
            province = #{province},
            city = #{city},
            county = #{county},
            town = #{town},
			village = #{village},
            address = #{address},
            detail = #{detail},
            lng = #{lng},
            lat = #{lat},
            company_introduction = #{companyIntroduction},
            ent_honor = #{entHonor},
			submit_date = #{submitDate},
            examine_status = #{examineStatus},
			examine_man = #{examineMan},
			examine_opinion = #{examineOpinion},
			examine_date = #{examineDate},
			table_name = #{tableName},
			table_id = #{tableId},
			basic_flag = #{basicFlag},
			basic_enter_flag = #{basicEnterFlag},
			certificate_amount = #{certificateAmount},
			certificate_print_amount = #{certificatePrintAmount},
			certificate_print_date = #{certificatePrintDate},
			inspextion_write_amount = #{inspectionWriteAmount},
            change_status = #{changeStatus},
            change_view_flag = #{changeViewFlag},
            change_opinion = #{changeOpinion},
			frozen_flag = #{frozenFlag},
			user_id = #{userId},
			data_scope = #{dataScope},
            update_by = #{updateBy.id},
            update_date = #{updateDate},
            remarks = #{remarks},
			last_modified = #{lastModified},
			sync_date = #{syncDate}
        WHERE id = #{id}
    </update>
    <update id="updateChangeView">
        UPDATE bas_ent SET
            change_view_flag = "1",
            update_by = #{updateBy.id},
            update_date = #{updateDate}
        WHERE id = #{id}
    </update>
    <update id="updateInvalidCertificateAmount">
        UPDATE bas_ent t
		INNER JOIN (
		    SELECT
		        c.ent_id AS entId,
		        SUM(IF(c.id!="" and c.del_flag = '0',1,0)) certificateAmount,
                SUM(IF(c.print_count!="" and c.del_flag = '0',c.print_count,0)) certificatePrintAmount,
				MAX(IF(c.del_flag = '0',c.create_date,null)) createDate
		    FROM
		        bas_certificate c
		    where c.ent_id = #{id}
			group by c.ent_id
		) a ON t.id = a.entId
		SET t.certificate_amount = a.certificateAmount,
		 t.certificate_print_amount = a.certificatePrintAmount,
		    t.certificate_print_date = a.createDate
		 where t.id = #{id}
    </update>
    <update id="updateFrozen">
        UPDATE bas_ent SET
            frozen_flag = #{frozenFlag},
            update_by = #{updateBy.id},
            update_date = #{updateDate}
        WHERE id = #{id}
    </update>
    <update id="updateTableId">
        UPDATE bas_ent SET
            table_id = #{tableId}
        WHERE id = #{id}
    </update>
    <!--主体信息详情-->
    <select id="findEntById" resultType="map">
        SELECT
        <include refid="entColumns"/>
        FROM bas_ent a
        WHERE
        a.del_flag = '0' and a.id = #{id}
    </select>
    <!--手机号唯一-->
    <select id="validateOnly" parameterType="string" resultType="int">
        SELECT
        ifNULL(count(1),0) cardSum
        FROM bas_ent a
        <where>
            a.del_flag = '0'
            <if test="null != contactsPhone and '' != contactsPhone">
                AND a.contacts_phone = #{contactsPhone}
            </if>
            <if test="null != id and '' != id">
                AND a.id != #{id}
            </if>
        </where>
    </select>

	<select id="validateIdCardOnly" parameterType="string" resultType="Integer">
		SELECT
		ifNULL(count(1),0) cardSum
		FROM bas_ent a
		<where>
			a.del_flag = '0'
			<if test="null != cardNo and '' != cardNo">
				AND a.card_no = #{cardNo}
			</if>
			<if test="null != id and '' != id">
				AND a.id != #{id}
			</if>
		</where>
	</select>

	<select id="getEntByIdCard" parameterType="string" resultType="com.thinkgem.jeesite.modules.bas.entity.Ent">
		SELECT
		<include refid="entColumns"/>
		FROM bas_ent a
		<where>
			del_flag = '0'
			and (a.card_no =#{cardNo} or a.contacts_phone =#{cardNo})
		</where>
	</select>

	<select id="getEntByTableId" parameterType="string" resultType="com.thinkgem.jeesite.modules.bas.entity.Ent">
		select
		<include refid="entColumns"/>
		from bas_ent a
		<where>
			del_flag = '0'
			and a.table_id = #{tableId}
		</where>
	</select>

	<select id="findAreaCheckDataList" resultType="map">
       SELECT
            <include refid="entColumns"/>
        FROM
            bas_check c
        INNER JOIN bas_ent a ON c.ent_id = a.id and a.del_flag = 0 AND a.examine_status = 1 AND a.frozen_flag=0
        WHERE
            c.check_type = 0
        AND c.del_flag = 0
        <if test="sqlMap.areaWhere!=null and sqlMap.areaWhere!=''">
            ${sqlMap.areaWhere}
        </if>
        <if test="areaCode != null and areaCode != '' ">
            AND a.county LIKE concat(#{areaCode},'%')
        </if>
        <if test="queryYear != null and queryYear != '' ">
            AND YEAR(c.check_date) = #{queryYear}
        </if>
        <if test="queryMonth != null and queryMonth != '' ">
            AND MONTH(c.check_date) = #{queryMonth}
        </if>
        <if test="entType != null and entType != '' ">
            AND a.ent_type = #{entType}
        </if>
        GROUP BY a.id
    </select>

    <select id="findAreaCertificateDataList" resultType="map">
        SELECT
            <include refid="entColumns"/>
        FROM bas_certificate c
        INNER JOIN bas_ent a ON c.ent_id = a.id and a.del_flag = '0' AND a.examine_status = '1' AND a.frozen_flag = '0'
        WHERE
        c.del_flag=0
        AND c.ent_main_type != ''
        <if test="areaCode != null and areaCode != '' ">
            AND a.county LIKE concat(#{areaCode},'%')
        </if>
        <if test="sqlMap.areaWhere!='' and sqlMap.areaWhere!=null">
            ${sqlMap.areaWhere}
        </if>
        <if test="queryYear != null and queryYear != '' ">
            AND YEAR(c.create_date) <![CDATA[ = ]]> #{queryYear}
        </if>
        <if test="queryMonth != null and queryMonth != '' ">
            AND MONTH(c.create_date) <![CDATA[ = ]]> #{queryMonth}
        </if>
        <if test="entType != null and entType != '' ">
            AND a.ent_type = #{entType}
        </if>
        GROUP BY a.id
    </select>
</mapper>
