<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thinkgem.jeesite.modules.bas.dao.CertificateDao">

	<sql id="certificateColumns">
		a.id AS "id",
		a.batch_no AS "batchNo",
		a.user_id AS "userId",
		a.data_scope AS "dataScope",
		a.no AS "no",
		a.product_id AS "productId",
		a.product_name AS "productName",
		a.product_introduction AS "productIntroduction",
		a.product_province AS "productProvince",
        a.product_city AS "productCity",
        a.product_county AS "productCounty",
		a.product_address AS "productAddress",
		a.product_detail AS "productDetail",
		a.product_sort_code AS "productSortCode",
		a.product_sort_name AS "productSortName",
		a.product_certification_code AS "productCertificationCode",
		a.product_certification_name AS "productCertificationName",
		a.product_num AS "productNum",
		a.product_unit_code AS "productUnitCode",
		a.product_unit_name AS "productUnitName",
		a.production_date AS "productionDate",
		a.ent_id AS "entId",
		a.ent_name AS "entName",
		a.ent_business_type AS "entBusinessType",
		a.ent_type AS "entType",
		a.ent_main_type AS "entMainType",
		a.ent_farm_type AS "entFarmType",
		a.ent_card_no AS "entCardNo",
		a.ent_legal_person AS "entLegalPerson",
		a.ent_province AS "entProvince",
		a.ent_city AS "entCity",
		a.ent_county AS "entCounty",
		a.ent_address AS "entAddress",
		a.ent_detail AS "entDetail",
		a.ent_social_code AS "entSocialCode",
		a.ent_contacts_phone AS "entContactsPhone",
		a.ent_autograph AS "entAutograph",
		a.ent_company_introduction AS "entCompanyIntroduction",
		a.ent_honor AS "entHonor",
		a.print_count AS "printCount",
		a.block_chain_id AS "blockChainId",
		a.begin_serial_number AS "beginSerialNumber",
		a.end_serial_number AS "endSerialNumber",
		a.inspection_situation AS "inspectionSituation",
		a.sample_no AS "sampleNo",
		a.product_inspection_id AS "productInspectionId",
		a.re_buy_visible AS "reBuyVisible",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.update_by AS "updateBy.id",
		a.update_date AS "updateDate",
		a.remarks AS "remarks",
		a.del_flag AS "delFlag",
		a.sync_date as "syncDate"
	</sql>

	<sql id="certificateJoins">
		INNER JOIN bas_product p ON p.id = a.product_id
	</sql>
    <sql id="certificateNoJoins">
        INNER JOIN bas_certificate_no n ON n.certificate_id = a.id
    </sql>
	<sql id="certificateEntJoins">
		INNER JOIN bas_ent e ON a.ent_id = e.id
	</sql>

	<select id="get" resultType="Certificate">
		SELECT
			<include refid="certificateColumns"/>
		FROM bas_certificate a
		<!--<include refid="certificateJoins"/>-->
		WHERE a.id = #{id}
	</select>

	<select id="findList" resultType="Certificate">
		SELECT
			<include refid="certificateColumns"/>
		FROM bas_certificate a
		<!--<include refid="certificateJoins"/>-->
		<include refid="certificateEntJoins"/>
		<if test="batchNo != null and batchNo != ''">
            <include refid="certificateNoJoins"/>
        </if>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			<if test="batchNo != null and batchNo != ''">
				AND n.full_number LIKE concat('%',#{batchNo},'')
			</if>
			<if test="productName != null and productName != ''">
			    <!--需求变更改为模糊查询 lxy 2021年5月25日9:40:13-->
				AND a.product_name LIKE concat('%',#{productName},'%')
			</if>
			<if test="productId != null and productId != ''">
				AND a.product_id = #{productId}
			</if>
			<if test="productSortCode != null and productSortCode != ''">
				AND a.product_sort_code = #{productSortCode}
			</if>
			<if test="printCount != null and printCount != ''">
				AND a.print_count = #{printCount}
			</if>
			<!-- <if test="beginCreateDate != null and endCreateDate != null and beginCreateDate != '' and endCreateDate != ''">
				AND a.create_date BETWEEN #{beginCreateDate} AND #{endCreateDate}
			</if> -->
			<if test="beginCreateDate != null and beginCreateDate != '' ">
                AND
                <![CDATA[ DATE_FORMAT(a.create_date,'%Y-%m-%d') >=DATE_FORMAT(#{beginCreateDate},'%Y-%m-%d') ]]>
            </if>
            <if test="endCreateDate != null and endCreateDate != '' ">
                AND
                <![CDATA[ DATE_FORMAT(a.create_date,'%Y-%m-%d') <=DATE_FORMAT(#{endCreateDate},'%Y-%m-%d') ]]>
            </if>

			<if test="time != null and time != ''">
				AND DATE_FORMAT(a.create_date,'%Y-%m-%d') = #{time}
			</if>
			<if test="entId != null and entId != ''">
				AND a.ent_id = #{entId}
			</if>
			<if test="entBusinessType != null and entBusinessType != ''">
				AND a.ent_business_type = #{entBusinessType}
			</if>
			<if test="entType != null and entType != ''">
				AND a.ent_type = #{entType}
			</if>
			<if test="entName != null and entName != ''">
				AND a.ent_name like "%"#{entName}"%"
			</if>
			<if test="entCode != null and entCode != ''">
				AND a.ent_county LIKE #{entCode}"%"
			</if>
			<if test="sqlMap.areaWhere!='' and sqlMap.areaWhere!=null">
				${sqlMap.areaWhere}
			</if>
			<if test="ent != null">
			    <if test="ent.frozenFlag != null and ent.frozenFlag != ''">
	                AND e.frozen_flag = #{ent.frozenFlag}
	            </if>
            </if>
			<if test="userId != null and userId != ''">
				AND a.user_id = #{userId}
			</if>
			<if test="syncDate != null">
				AND Date(a.update_date) <![CDATA[ >= ]]> Date(#{syncDate})
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>

	<insert id="insert">
		INSERT INTO bas_certificate(
			id,
			batch_no,
			user_id,
			data_scope,
			no,
			product_id,
			product_name,
			product_sort_code,
			product_sort_name,
			product_num,
			product_unit_code,
			product_unit_name,
			production_date,
			print_count,
			begin_serial_number,
            end_serial_number,
			re_buy_visible,
			create_by,
			create_date,
			update_by,
			update_date,
			remarks,
			del_flag
		) VALUES (
			#{id},
			#{batchNo},
			#{userId},
			#{dataScope},
			#{no},
			#{productId},
			#{productName},
			#{productSortCode},
			#{productSortName},
			#{productNum},
			#{productUnitCode},
			#{productUnitName},
			#{productionDate},
			#{printCount},
			#{beginSerialNumber},
			#{endSerialNumber},
			#{reBuyVisible},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate},
			#{remarks},
			#{delFlag}
		)
	</insert>

	<insert id="insertAll">
		INSERT INTO bas_certificate(
			id,
			batch_no,
			user_id,
			data_scope,
			no,
			product_id,
			product_name,
			product_introduction,
			product_province,
            product_city,
            product_county,
			product_address,
			product_detail,
			product_sort_code,
			product_sort_name,
			product_certification_code,
		    product_certification_name,
			product_num,
			product_unit_code,
			product_unit_name,
			production_date,
			ent_id,
			ent_name,
			ent_business_type,
			ent_type,
			ent_main_type,
			ent_farm_type,
			ent_card_no,
			ent_legal_person,
			ent_province,
			ent_city,
			ent_county,
			ent_address,
			ent_detail,
			ent_social_code,
			ent_contacts_phone,
			ent_autograph,
			ent_company_introduction,
			ent_honor,
			print_count,
		    block_chain_id,
			begin_serial_number,
            end_serial_number,
            inspection_situation,
			sample_no,
			product_inspection_id,
			re_buy_visible,
			create_by,
			create_date,
			update_by,
			update_date,
			remarks,
			del_flag,
		    sync_date
		) VALUES (
			#{id},
			#{batchNo},
			#{userId},
			#{dataScope},
			#{no},
			#{product.id},
			#{product.name},
			#{product.productIntroduction},
			#{product.province},
			#{product.city},
			#{product.county},
			#{product.address},
			#{product.detail},
			#{product.productSortCode},
			#{product.productSortName},
			#{product.productCertificationCode},
			#{product.productCertificationName},
			#{productNum},
			#{productUnitCode},
			#{productUnitName},
			#{productionDate},
			#{ent.id},
			#{ent.name},
			#{ent.businessType},
			#{ent.entType},
			#{ent.mainType},
			#{ent.farmType},
			#{ent.cardNo},
			#{ent.legalPerson},
			#{ent.province},
			#{ent.city},
			#{ent.county},
			#{ent.address},
			#{ent.detail},
			#{ent.socialCode},
			#{ent.contactsPhone},
			#{ent.autograph},
			#{ent.companyIntroduction},
			#{ent.entHonor},
			#{printCount},
		    #{blockChainId},
			#{beginSerialNumber},
			#{endSerialNumber},
			#{inspectionSituation},
			#{product.currentSampleNo},
			#{productInspectionId},
			#{reBuyVisible},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate},
			#{remarks},
			#{delFlag},
			#{syncDate}
		)
	</insert>

	<update id="update">
		UPDATE bas_certificate SET
			batch_no = #{batchNo},
			user_id = #{userId},
			data_scope = #{dataScope},
			no = #{no},
			product_id = #{productId},
			product_name = #{productName},
			product_instruction = #{productIntroduction},
			product_province = #{productProvince},
			product_city = #{productCity},
			product_county = #{productCounty},
			product_address = #{productAddress},
			product_detail = #{productDetail},
			product_sort_code = #{productSortCode},
			product_sort_name = #{productSortName},
			product_certification_code = #{productCertificationCode},
			product_certification_name = #{productCertificationName},
			product_num = #{productNum},
			product_unit_code = #{productUnitCode},
			product_unit_name = #{productUnitName},
			ent_id = #{entId},
			ent_name = #{entName},
			ent_business_type = #{entBusinessType},
			ent_type = #{entType},
			ent_main_type = #{entMainType},
			ent_farm_type = #{entFarmType},
			ent_card_no = #{entCardNo},
			ent_legal_person = #{entLegalPerson},
			ent_province = #{entProvince},
			ent_city = #{entCity},
			ent_county = #{entCounty},
			ent_address = #{entAddress},
			ent_detail = #{entDetail},
			ent_social_code = #{entSocialCode},
			ent_contacts_phone = #{entContactsPhone},
			ent_autograph = #{entAutograph},
			ent_company_introduction = #{entCompanyIntroduction},
			ent_honor = #{entHonor},
			production_date = #{productionDate},
			print_count = #{printCount},
			block_chain_id = #{blockChainId},
			begin_serial_number = #{beginSerialNumber},
			end_serial_number = #{endSerialNumber},
			inspection_situation = #{inspectionSituation},
			sample_no = #{sampleNo},
			product_inspection_id = #{productInspectionId},
			re_buy_visible = #{reBuyVisible},
			update_by = #{updateBy.id},
			update_date = #{updateDate},
			remarks = #{remarks},
			sync_date = #{syncDate}
		WHERE id = #{id}
	</update>

	<update id="updatePrintCount">
		UPDATE bas_certificate SET
			print_count = #{printCount},
			update_by = #{updateBy.id},
			update_date = #{updateDate}
		WHERE id = #{id}
	</update>

	<update id="delete">
		UPDATE bas_certificate SET
			del_flag = #{DEL_FLAG_DELETE}
		WHERE id = #{id}
	</update>

	<select id="searchNextNo" resultType="Integer">
		SELECT IFNULL(MAX(no)+1,1) AS nextNo FROM bas_certificate
		where ent_county=#{code} AND DATE(create_date) = DATE(NOW());
	</select>

	<select id="getFrequencyByEntId" resultType="map">
		SELECT
			count(a.id) AS "certificateCount",
			IFNULL(SUM(a.print_count),0) AS "certificatePrintCount"
		FROM bas_certificate a
		<include refid="certificateJoins"/>
		WHERE p.ent_id = #{id}
	</select>
	<select id="getFrequencyByCountyCode" resultType="map">
		SELECT
			count(a.id) AS "certificateCountAmount",
			IFNULL(SUM(a.print_count),0) AS "certificatePrintCountAmount"
		FROM bas_certificate a
		<!-- <include refid="certificateJoins"/>
		<include refid="certificateEntJoins"/> -->
		INNER JOIN bas_ent e ON a.ent_id = e.id and e.frozen_flag='0'
		WHERE a.ent_county LIKE CONCAT(#{entCounty},'%')
		<if test="sqlMap.areaWhere!='' and sqlMap.areaWhere!=null">
			${sqlMap.areaWhere}
		</if>
		AND a.ent_main_type != ''
		and a.del_flag = 0
	</select>

	<select id="getSummaryInfo" resultType="map">
        SELECT
            COUNT(a.id) dataAmount,
            IFNULL(SUM(a.print_count),0) printAmount,
            MAX(DATE(a.create_date)) lastCreateDate
        FROM bas_certificate a
        WHERE a.ent_id = #{entId}
        and a.del_flag = 0
    </select>

	<update id="updateBlockChainId">
		UPDATE bas_certificate SET
			block_chain_id = #{blockChainId},
			remarks = '绑定区块链',
			update_by = null,
			update_date = now()
		WHERE id = #{id}
	</update>

	<select id="findInvalidList" resultType="Certificate">
        SELECT
            <include refid="certificateColumns"/>
        FROM bas_certificate a
        <!--<include refid="certificateJoins"/>-->
        <if test="batchNo != null and batchNo != ''">
            <include refid="certificateNoJoins"/>
        </if>
        <where>
            a.del_flag = #{DEL_FLAG_DELETE}
            <if test="batchNo != null and batchNo != ''">
                AND n.full_number LIKE concat('%',#{batchNo},'')
            </if>
            <if test="productName != null and productName != ''">
                <!--需求变更改为模糊查询 lxy 2021年5月25日9:40:13-->
                AND a.product_name LIKE concat('%',#{productName},'%')
            </if>
            <if test="productId != null and productId != ''">
                AND a.product_id = #{productId}
            </if>
            <if test="productSortCode != null and productSortCode != ''">
                AND a.product_sort_code = #{productSortCode}
            </if>
            <if test="printCount != null and printCount != ''">
                AND a.print_count = #{printCount}
            </if>
            <!-- <if test="beginCreateDate != null and endCreateDate != null and beginCreateDate != '' and endCreateDate != ''">
                AND a.create_date BETWEEN #{beginCreateDate} AND #{endCreateDate}
            </if> -->
            <if test="beginCreateDate != null and beginCreateDate != '' ">
                AND
                <![CDATA[ DATE_FORMAT(a.create_date,'%Y-%m-%d') >=DATE_FORMAT(#{beginCreateDate},'%Y-%m-%d') ]]>
            </if>
            <if test="endCreateDate != null and endCreateDate != '' ">
                AND
                <![CDATA[ DATE_FORMAT(a.create_date,'%Y-%m-%d') <=DATE_FORMAT(#{endCreateDate},'%Y-%m-%d') ]]>
            </if>

            <if test="time != null and time != ''">
                AND DATE_FORMAT(a.create_date,'%Y-%m-%d') = #{time}
            </if>
            <if test="entId != null and entId != ''">
                AND a.ent_id = #{entId}
            </if>
            <if test="entBusinessType != null and entBusinessType != ''">
                AND a.ent_business_type = #{entBusinessType}
            </if>
            <if test="entType != null and entType != ''">
                AND a.ent_type = #{entType}
            </if>
            <if test="entName != null and entName != ''">
                AND a.ent_name like "%"#{entName}"%"
            </if>
            <if test="entCode != null and entCode != ''">
                AND a.ent_county LIKE #{entCode}"%"
            </if>
			<if test="sqlMap.areaWhere!='' and sqlMap.areaWhere!=null">
				${sqlMap.areaWhere}
			</if>
        </where>
        <choose>
            <when test="page !=null and page.orderBy != null and page.orderBy != ''">
                ORDER BY ${page.orderBy}
            </when>
            <otherwise>
                ORDER BY a.update_date DESC
            </otherwise>
        </choose>
    </select>

	<select id="findPrintTotalNum" resultType="Integer">
		SELECT IFNULL(sum(a.print_count),0) AS printTotalNum FROM bas_certificate a
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL}
			<if test="entId != null and entId != ''">
                AND a.ent_id = #{entId}
            </if>
			<if test="userId != null and userId != ''">
				AND a.user_id = #{userId}
			</if>
			<if test="productName != null and productName != ''">
				AND a.product_name LIKE concat('%',#{productName},'%')
			</if>
			<if test="entName != null and entName != ''">
				AND a.ent_name like concat('%',#{entName},'%')
			</if>
		</where>
	</select>

	<select id="selectCertificateCount" resultType="int">
		SELECT
			COUNT(a.id)
		FROM bas_certificate a
		WHERE a.del_flag = 0
		<if test="syncDate != null">
			AND Date(a.update_date) <![CDATA[ >= ]]> Date(#{syncDate})
		</if>
	</select>
</mapper>
