#============================#
#===== Database sttings =====#
#============================#

#oracle database settings
#jdbc.type=oracle
#jdbc.driver=oracle.jdbc.driver.OracleDriver
#jdbc.url=*************************************
#jdbc.username=jeesite
#jdbc.password=123456

#mysql database setting
jdbc.type=mysql
jdbc.driver=com.mysql.cj.jdbc.Driver
#    jdbc.url=*********************************************************************************************************************************
##jdbc.url=****************************************************************************************************************************
#jdbc.username=root
#jdbc.password=sxkj0818web
jdbc.url=jdbc:mysql://***************:3306/xm_certificate_241226?useUnicode=true&characterEncoding=utf-8&serverTimezone=GMT%2B8&useSSL=false
jdbc.username=root
jdbc.password=zhsa-kf-123456
#jdbc.password=sxkj0818
#jdbc.url=***************************************************************************************************************************
#jdbc.username=root
#jdbc.password=123456
#mssql database settings
#jdbc.type=mssql
#jdbc.driver=net.sourceforge.jtds.jdbc.Driver
#jdbc.url=********************************************
#jdbc.username=sa
#jdbc.password=sa

#pool settings
jdbc.pool.init=1
jdbc.pool.minIdle=3
jdbc.pool.maxActive=20

#jdbc.testSql=SELECT 'x'
jdbc.testSql=SELECT 'x' FROM DUAL

#redis settings
redis.keyPrefix=xmCertificate
redis.host=127.0.0.1
redis.port=6379
redis.password=918273
#\u6BEB\u79D2
redis.timeout=0
#============================#
#===== System settings ======#
#============================#

#\u4EA7\u54C1\u4FE1\u606F\u8BBE\u7F6E
productName=\u98DF\u7528\u519C\u4EA7\u54C1\u627F\u8BFA\u8FBE\u6807\u5408\u683C\u8BC1\u670D\u52A1\u76D1\u7BA1\u7CFB\u7EDF
powerBy=\u5409\u6797\u7701\u68EE\u7965\u79D1\u6280\u6709\u9650\u516C\u53F8
powerBy2=\u5409\u6797\u7701\u5409\u79D1\u8F6F\u4FE1\u606F\u6280\u672F\u6709\u9650\u516C\u53F8
copyrightYear=2018
version=V1.0.0
author=waile23
author.website=http://www.plusdo.com

#\u8C03\u8BD5\u6A21\u5F0F
debugMode=false

#\u6F14\u793A\u6A21\u5F0F: \u4E0D\u80FD\u64CD\u4F5C\u548C\u4FDD\u5B58\u7684\u6A21\u5757\uFF1A sys: area/office/user/role/menu/dict, cms: site/category
demoMode=false

#\u7BA1\u7406\u57FA\u7840\u8DEF\u5F84, \u9700\u540C\u6B65\u4FEE\u6539\uFF1Aweb.xml
adminPath=/a

#\u524D\u7AEF\u57FA\u7840\u8DEF\u5F84
frontPath=/f

#API\u57FA\u7840\u8DEF\u5F84
apiPath=/rest

syncApiPath=/syncData

#\u7F51\u7AD9URL\u540E\u7F00
urlSuffix=.html

#\u662F\u5426\u4E0D\u5141\u8BB8\u5237\u65B0\u4E3B\u9875\uFF0C\u4E0D\u5141\u8BB8\u60C5\u51B5\u4E0B\uFF0C\u5237\u65B0\u4E3B\u9875\u4F1A\u5BFC\u81F4\u91CD\u65B0\u767B\u5F55
notAllowRefreshIndex=false

#\u662F\u5426\u5141\u8BB8\u591A\u8D26\u53F7\u540C\u65F6\u767B\u5F55
user.multiAccountLogin=true

#\u5206\u9875\u914D\u7F6E
page.pageSize=20

#\u7855\u6B63\u7EC4\u4EF6\u662F\u5426\u4F7F\u7528\u7F13\u5B58
supcan.useCache=false

#\u901A\u77E5\u95F4\u9694\u65F6\u95F4\u8BBE\u7F6E, \u5355\u4F4D\uFF1A\u6BEB\u79D2, 30s=30000ms, 60s=60000ms
oa.notify.remind.interval=60000

#============================#
#====    restful api     ====#
#============================#
#\u5206\u9875\u914D\u7F6E
api.page.pageSize=10

#============================#
#====    restful api     ====#
#============================#
#\u5FAE\u4FE1\u6A21\u5F0F,true\u662F\u5F00\u542F\u5FAE\u4FE1\u6A21\u5F0F\uFF0C\u524D\u7AEF\u53EA\u80FD\u5728\u5FAE\u4FE1\u4E2D\u4F7F\u7528,false\u5173\u95ED\u5FAE\u4FE1\u6A21\u5F0F\uFF0C\u53EF\u4EE5\u5728\u6D4F\u89C8\u5668\u4E2D\u8C03\u8BD5
wechatMode=true
#\u5FAE\u4FE1\u57FA\u7840\u8DEF\u5F84
wechatPath=/wechat
#\u5FAE\u4FE1\u516C\u4F17\u53F7oauth\u9A8C\u8BC1\u540E\uFF0C\u8FD4\u56DE\u5230\u524D\u7AEF\u7684\u8DF3\u8F6C\u9875\u9762
wechat.redirect.url=http://www.plusdo.com:8080

#============================#
#==== Framework settings ====#
#============================#

#\u4F1A\u8BDD\u8D85\u65F6\uFF0C \u5355\u4F4D\uFF1A\u6BEB\u79D2\uFF0C 20m=1200000ms, 30m=1800000ms, 60m=3600000ms
session.sessionTimeout=1800000
#\u4F1A\u8BDD\u6E05\u7406\u95F4\u9694\u65F6\u95F4\uFF0C \u5355\u4F4D\uFF1A\u6BEB\u79D2\uFF0C2m=120000ms\u3002
session.sessionTimeoutClean=120000

#\u7F13\u5B58\u8BBE\u7F6E
ehcache.configFile=cache/ehcache-local.xml
#ehcache.configFile=cache/ehcache-rmi.xml

#\u7D22\u5F15\u9875\u8DEF\u5F84
web.view.index=/a

#\u89C6\u56FE\u6587\u4EF6\u5B58\u653E\u8DEF\u5F84
web.view.prefix=/WEB-INF/views/
web.view.suffix=.jsp

#\u6700\u5927\u6587\u4EF6\u4E0A\u4F20\u9650\u5236\uFF0C\u5355\u4F4D\u5B57\u8282. 10M=10*1024*1024(B)=10485760 bytes
#web.maxUploadSize=10485760
web.maxUploadSize=5242880000

#\u65E5\u5FD7\u62E6\u622A\u8BBE\u7F6E\uFF0C\u6392\u9664\u7684URI\uFF1B\u5305\u542B @RequestMapping\u6CE8\u89E3\u7684value\u3002\uFF08\u5DF2\u4F5C\u5E9F\uFF09
#web.logInterceptExcludeUri=/, /login, /sys/menu/tree, /sys/menu/treeData
#web.logInterceptIncludeRequestMapping=save, delete, import, updateSort

#\u9759\u6001\u6587\u4EF6\u540E\u7F00
web.staticFile=.css,.js,.png,.jpg,.gif,.jpeg,.bmp,.ico,.swf,.psd,.htc,.htm,.html,.crx,.xpi,.exe,.ipa,.apk
web.testPassword=@Aa13579
web.testRoleId=1390260856398282752
#\u5355\u70B9\u767B\u5F55CAS\u8BBE\u7F6E
cas.server.url=http://127.0.0.1:8180/cas
cas.project.url=http://127.0.0.1:8080/jeesite

#\u5DE5\u7A0B\u8DEF\u5F84\uFF0C\u5728\u4EE3\u7801\u751F\u6210\u65F6\u83B7\u53D6\u4E0D\u5230\u5DE5\u7A0B\u8DEF\u5F84\u65F6\uFF0C\u53EF\u518D\u6B64\u6307\u5B9A\u7EDD\u5BF9\u8DEF\u5F84\u3002
#projectPath=D\:\\workspace\\jeesite
filePath=D\:\\sxkj\\attachment

#\u884C\u653F\u533A\u5212\u7B2C\u4E00\u7EA7parent_id
rootAreaId=000000000000

viewCertificateUrl=http://***************:8083/xmCertificate

#\u9A8C\u8BC1\u7801\u6F14\u793A\u6A21\u5F0F
captchaDemoMode=true
#\u77ED\u4FE1\u9650\u5236\u4E00\u4E2A\u624B\u673A\u53F7\u65E5\u53D1\u9001\u6B21\u6570
verifyDaySamePhoneCnt=5
#\u77ED\u4FE1\u9650\u5236\u4E00\u4E2Aip\u65E5\u53D1\u9001\u6B21\u6570
verifyDaySameIpCnt=10

#\u4E03\u725B
ACCESS_KEY=988cBjoifTDqC-TEdNH1CLWhBKUYHs4GMcqjplm-
SECRET_KEY=l1KowQsQiFcbc9-LIrCj9L20riAl4Ox6rgOFp3cq
BUCKET=sxkj-dev
#QIUNIU_DOMAIN=http://devqn.jlsenxiang.com/
QIUNIU_DOMAIN=https://devqn.jikeruan.com/

#\u533A\u5757\u8FDE\u5F00\u5173
blockChainEnabled=false
#\u533A\u5757\u94FE\u63A5\u53E3\u5730\u5740
blockChain.baseUrl=http://***************:20006
blockChain.insertUrl=/fabric/api/v1/insert
blockChain.queryByIdUrl=/fabric/api/v1/getQueryResultByTxId
blockChain.checkByIdUrl=/fabric/api/v1/checkByTxId

#\u963F\u91CC\u56FE\u7247\u8BC6\u522B
aliapi.accessCode=b55ed72071cf470ca64a59e2a0c4249a

#\u6D41\u6C34\u53F7\u9ED8\u8BA4\u957F\u5EA6
serialNumberLength=7



#\u767E\u5EA6\u4E91\u56FE\u7247\u8BC6\u522B
baidu.appId=ce48483e0f8e4ac282834172d04f53a6
baidu.appKey=f059cf3dd441422da2249de5dd31025d
baidu.secret=c0e62b79fbad4e7983620021147eaeeb

#\u56FE\u7247\u8BC6\u522B\u65B9\u5F0F\uFF08alibaba/baidu\uFF09
ocrType=alibaba

#\u662F\u5426\u542F\u7528\u5206\u5E03\u5F0F\u7F13\u5B58
cache.distributed.enable=true

#\u7CFB\u7EDF\u5F00\u59CB\u8FD0\u8425\u65F6\u95F4
systemOnlineTime=2020-08-01

#\u6570\u636E\u5206\u6790\u5C55\u677Furl
panelUrl=http://***************:8455/webroot/decision/view/form?viewlet=agriculture%252FxmCertificate.frm


#\u6837\u54C1\u7F16\u7801\u67E5\u8BE2\u63A5\u53E3
inspection.url=http://192.168.189.146:8088/inspection
inspection.queryBySampleNoUrl=/rest/test/findRecordListByNo
#\u6839\u636E\u6837\u54C1\u7F16\u53F7\u67E5\u8BE2\u5408\u683C\u6700\u65B0\u7ED3\u679C
inspection.queryQualifiedResultBySampleUrl=/rest/test/findRecordListByNoLatest
#\u68C0\u6D4B\u6837\u54C1\u53D1\u9001\u63A5\u53E3
inspection.saveSampleUrl=/rest/task/task/create/batch
#\u68C0\u6D4B\u6837\u54C1\u83B7\u53D6\u589E\u91CF\u6570\u636E\u63A5\u53E3
inspection.findRecordListByDate=/rest/test/findRecordListByDate
#\u5FEB\u68C0\u7CFB\u7EDF\u8EAB\u4EFD\u6807\u8BC6
inspection.AccessKey=hgz
#\u5FEB\u68C0\u79D8\u94A5
inspection.SecretKey=4028e4d455487d86015548ec043d02d5

#\u5FEB\u68C0\u63A5\u53E3\u5F00\u5173
inspectionEnabled=false
#\u5FEB\u68C0\u83B7\u53D6\u6570\u636E\u63A5\u53E3\u5B9A\u65F6\u914D\u7F6E
inspection.job=0 */30 * * * ?

#\u57FA\u7840\u4FE1\u606F\u5BF9\u63A5\u5F00\u5173
basicRestEnabled=false

#\u57FA\u7840\u4FE1\u606F\u63A5\u53E3\u914D\u7F6E
basicRest.token=certificate_17dbc0d380fc4b5a8a838f0f37a27bff
basicRest.url=http://127.0.0.1:9080
basicRest.finishDataCountFetchByTel=/wechat/rest/entFarm/finishDataCountFetchByTel
basicRest.saveOnFinishStatus=/wechat/rest/entFarm/saveOnFinishStatus
basicRest.fetchStreetsByCountyCode=/wechat/rest/entFarm/fetchStreetsByCountyCode

#\u5BF9\u63A5token
jkr.token=jkr_72a58b88562a4301b3ca631e068b8835
sxkjxy.token=sxkjxy_918730a609a643559ee5821981e1e439

#\u7CFB\u7EDF\u63A5\u53E3\u57FA\u7840\u8DEF\u5F84
systemPath=/system

#\u84DD\u7259\u8BBE\u5907\u6821\u9A8C\u6388\u6743\u5F00\u5173
bluetoothDeviceVerifyEnabled=false

#============================#
#===== schedule sttings =====#
#============================#
#\u6838\u5FC3\u7EBF\u7A0B\u6570
schedule.poolSize=10
#\u6700\u5927\u7EBF\u7A0B\u6570
schedule.maxPoolSize=200
#\u961F\u5217\u5927\u5C0F
schedule.queueCapacity=1000000
#\u7EBF\u7A0B\u6C60\u4E2D\u7684\u7EBF\u7A0B\u540D\u79F0\u524D\u7F00
schedule.threadNamePrefix=jobThreadPool-

#\u4E3B\u57CE\u533A\u5F00\u5173
officeAreaEnabled=false

#=================================#
#==== dataCentre\u6570\u636E\u4E2D\u5FC3 sttings ====#
#=================================#
#\u63A8\u9001\u5F00\u5173
dataCentrePushEnabled=false
#appid
dataCentre.appId=bt9333503358861312
#\u79D8\u94A5
dataCentre.secret=80bde89d53121b64a47c80487cb14273
#\u63A8\u9001\u533A\u57DF\u7F16\u7801(\u5E02\u7EA7)
dataCentre.pushAreaCode=2206
#\u63A8\u9001\u670D\u52A1url
dataCentre.gateway: http://192.168.183.126:8090/traceAnalysisServer
#\u63A8\u9001\u8FFD\u6EAF\u63A5\u53E3
dataCentre.traceApi:/open/gateway/tracechain
#\u6570\u636E\u5904\u7406\u9519\u8BEF\u56DE\u8C03
dataCentre.notifyUrl: http://192.168.183.126:8080/
#\u5409\u6797\u7701\u5C55\u677F\u524D\u7AEF\u5730\u5740
boardUrl= http://192.168.181.122:1760/#/statisticsIndex/

#=================================#
#======= \u7965\u4E91\u5BF9\u63A5 settings =======#
#=================================#
#\u516C\u94A5
xiangYun.ak=ed01106c46df4394b9db84bb1e1fd49f
#\u79C1\u94A5
xiangYun.sk=117fd24a274e495e9f33b10e232cb66a
#appId
xiangYun.appId=3ce1470de2834cdd95f4e8743c2ec7ef
#\u7965\u4E91\u63A5\u53E3\u5730\u5740\u8DEF\u5F84
xiangYun.url=https://111.26.49.17:8343

#=================================#
#==== \u9AD8\u5FB7 sttings ====#
#=================================#
#key
gaode.key=f380e703a319e812f3aea552cf2bb490

#\u9F99\u4E95\u7EDF\u4E00\u95E8\u6237
sso.server.url = http://192.168.190.173:8084/sso/app/authAppByAppId
sso.server.loginName.url = http://192.168.190.173:8084/sso/app/authAppLoginName
sso.appId = 620c6213e4c5c13c8e2357ea
sso.appSecret = 903ccf32d88186e5aa060e5472958659
sso.binding.url = http://192.168.190.173:8084/sso/app/binding

shouye.usedes.url = https://jlsyncphgzqn.jikeruan.com/img/image_text1.png
shouye.product.url = https://jlsyncphgzqn.jikeruan.com/img/image_text2.jpg
shouye.zhenxuan.url = https://jlsyncphgzqn.jikeruan.com/img/ynyp_car.jpg

source.checkPhoneNum.url= http://172.16.183.201:9191/certificate/getFlagByCertificate
product.reBuyProduct.url= http://172.16.183.201:9191/system/goods/getGoodsListByPhone

#=================================#
#==== Product???? settings ====#
#=================================#
# Product???????true????false????
product.sync.enabled=true
# ????URL???????????
product.sync.api.url=http://external-api.com/products/incremental
# ????????????
product.sync.api.timeout=30000
# ????????
product.sync.batch.size=100
# ????????????????????
product.sync.time.format=yyyy-MM-dd HH:mm:ss
