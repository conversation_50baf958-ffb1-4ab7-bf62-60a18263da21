package com.github.panchitoboy.shiro.wechat.realm;

import java.util.List;
import java.util.Map;

import com.thinkgem.jeesite.modules.xiangyun.service.XiangYunService;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.SimpleAccount;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.subject.PrincipalCollection;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.github.panchitoboy.shiro.wechat.filter.WechatJWTAuthenticationToken;
import com.github.panchitoboy.shiro.wechat.repository.AuthenticationService;
import com.github.panchitoboy.shiro.wechat.repository.UserInfo;
import com.github.panchitoboy.shiro.wechat.repository.WechatUserRepository;
import com.thinkgem.jeesite.common.config.Global;
import com.thinkgem.jeesite.common.config.GlobalConst;
import com.thinkgem.jeesite.common.utils.CacheUtils;
import com.thinkgem.jeesite.modules.wechat.user.entity.WechatUser;

import static com.thinkgem.jeesite.modules.sys.utils.SysConstants.MEMBER_JISHIBAN_REMARKS;
import static com.thinkgem.jeesite.modules.sys.utils.SysConstants.XIANG_YUN_EXCEPTION_MESSAGE;


@Service
public class WechatJwtRealm extends AuthorizingRealm {

    private Logger logger = LoggerFactory.getLogger(getClass());
    @Autowired
    @Qualifier("memberUserService") //指定实现类
	private AuthenticationService authenticationService;

	@Autowired
	private WechatUserRepository wechatUserRepository;

	@Autowired
	private XiangYunService xiangYunService;

    @Override
    public boolean supports(AuthenticationToken token) {
        return token != null && token instanceof WechatJWTAuthenticationToken;
    }
    /**
     * 验证函数
     */
    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken token) {
    	WechatJWTAuthenticationToken wechatToken = (WechatJWTAuthenticationToken) token;
    	String phoneNumber = wechatToken.getPhoneNumber()!=null?wechatToken.getPhoneNumber().toString():"";
    	//使用手机号、短信验证码、微信传回的code登录时
    	if(wechatToken.isLogin()) {
    		if (logger.isDebugEnabled()){
    			logger.debug("login submit, phoneNumber: {}", phoneNumber);
    		}
    		//是否为祥云登录
    		boolean xiangYunFlag = false;
    		String name = "";
			//祥云前端传递的authCode
			String xiangYunAuthCode = wechatToken.getXiangYunAuthCode();
			if(StringUtils.isNotBlank(xiangYunAuthCode)){
				xiangYunFlag = true;
				//获取祥云accessToken
				String accessToken = xiangYunService.idsNaturalGetTokenByAuthCode(xiangYunAuthCode);
				if(StringUtils.isBlank(accessToken)){
					throw new AuthenticationException(XIANG_YUN_EXCEPTION_MESSAGE + "未取到accessToken！");
				}
				//获取祥云用户信息
				Map<String,Object> map = xiangYunService.idsNaturalGetInfo(accessToken);
				if(map.isEmpty()){
					throw new AuthenticationException(XIANG_YUN_EXCEPTION_MESSAGE + "未取到用户信息！");
				}
				//只用到了电话号和昵称
				wechatToken.setPhoneNumber(map.get("mobileNo").toString());
				phoneNumber = map.get("mobileNo").toString();
				name = map.get("certName").toString();
			}
    		//如果是微信模式
    		/*if(Config.isWechatMode()) {
	    		if(StringUtils.isBlank(wechatToken.getCode())) {
	    			throw new AuthenticationException("msg:请您在微信上登录！或退出当前页面重新进入登录页面");
	    		}
    		}*/

    		if(StringUtils.isBlank(wechatToken.getCaptcha())&&!xiangYunFlag) {
    			throw new AuthenticationException("msg:请您输入验证码！");
    		}

    		/*if(CacheUtils.get(GlobalConst.CAPTCHA_CACHE, phoneNumber) == null) {
    			throw new AuthenticationException("msg:您输入的手机号与验证码不相符，请重新登录！");
    		}

    		if (!((String)CacheUtils.get(GlobalConst.CAPTCHA_CACHE, phoneNumber)).equals(wechatToken.getCaptcha())) {
    			throw new AuthenticationException("msg:您输入的手机号与验证码不相符，请重新登录！");
    		}*/
//    		boolean flag=true;
//			try {
//				if(!xiangYunFlag){
//					flag = authenticationService.checkVerificationCode(phoneNumber, wechatToken.getCaptcha());
//				}
//			} catch (Exception e) {
//				e.printStackTrace();
//			}
//			if(!flag) {
//    			throw new AuthenticationException("msg:您输入的手机号与验证码不相符，请重新登录！");
//    		}

    		//在缓存中获得openId
    		//String openId = CacheUtils.get(GlobalConst.WECHAT_USER_CACHE, wechatToken.getCode()) == null ? "" : (String)CacheUtils.get(GlobalConst.WECHAT_USER_CACHE, wechatToken.getCode()) ;
    		String openId="";
    		JSONObject wechatUser=null;
    		if(StringUtils.isNotBlank(wechatToken.getCode())) {
    			wechatUser=JSONObject.parseObject(CacheUtils.get(GlobalConst.WECHAT_USER_CACHE, wechatToken.getCode()).toString());
    			openId=wechatUser.getString("openId");
    		}


    		UserInfo userInfo = null;

    		//获得用户信息
    		if(StringUtils.isNotBlank(openId)) {
				userInfo = authenticationService.getUserInfoByPhoneNumber(wechatToken.getPhoneNumber().toString(), openId);
			}else {
				userInfo = authenticationService.getUserInfoByPhoneNumber(wechatToken.getPhoneNumber().toString());
			}
    		if(userInfo != null && Global.NO.equals(userInfo.getLoginFlag())) {
    			throw new AuthenticationException("msg:该已帐号禁止登录.");
    		}

    		//如果没有信息则注册会员
    		if(userInfo == null) {
    			//在缓存中通过openId获得微信用户信息
    			//WxMpUser wxMpUser = (WxMpUser)CacheUtils.get(GlobalConst.WECHAT_USER_CACHE, openId) ;
    			String memberName = wechatUser == null ? phoneNumber : wechatUser.getString("nickName");
    			String remarks = "";
    			if(xiangYunFlag){
					memberName = name;
					//标记吉事办用户
					remarks = MEMBER_JISHIBAN_REMARKS;
				}
    			String genderCode = wechatUser == null ? "-1" : wechatUser.getString("gender");
    			userInfo = authenticationService.register(memberName, phoneNumber, genderCode, openId, remarks);
    		}

    		//保存、更新微信用户信息
			if (null != wechatUser && StringUtils.isNotBlank(openId)) {
				String useid = userInfo.getId();
				String tableName = "bas_member";
				String nickName = wechatUser.getString("nickName");
				String userLanguage = wechatUser.getString("language");
				String province = wechatUser.getString("province");
				String city = wechatUser.getString("city");
				String country = wechatUser.getString("country");
				String headimageurl = wechatUser.getString("avatarUrl");
				String unionid = wechatUser.getString("unionid");
				Integer sexid = wechatUser.getInteger("gender");

				WechatUser wechatParam = new WechatUser();
				wechatParam.setUseid(useid);
				List<WechatUser> wechatList = authenticationService.findListWechatUser(wechatParam);
				if (wechatList.isEmpty()) {
					authenticationService.saveWechatUser(useid, tableName, openId, nickName, userLanguage, city,
							province, country, headimageurl, unionid, sexid);
				} else {
					WechatUser weUser = wechatList.get(0);
					weUser.setNickName(nickName);
					weUser.setUserLanguage(userLanguage);
					weUser.setProvince(province);
					weUser.setCity(city);
					weUser.setCountry(country);
					weUser.setHeadimageurl(headimageurl);
					weUser.setUnionid(unionid);
					weUser.setSexid(sexid);
					authenticationService.updateWechatUser(weUser);
				}
			}


    		//创建shiro用户信息
			SimpleAccount account = new SimpleAccount(userInfo, wechatToken.getCredentials(), getName());
            return account;

            //throw new AuthenticationException("没有该用户信息！");

    	} else { //验证token时
    		if(!wechatUserRepository.validateToken(wechatToken.getToken())) {
    			throw new AuthenticationException("Token已失效！");
    		}
    		UserInfo userInfo = authenticationService.getUserInfoByPhoneNumber(wechatToken.getPhoneNumber().toString());
            if (userInfo != null) {
            	SimpleAccount account = new SimpleAccount(userInfo, wechatToken.getToken(), getName());
                return account;
            } else {
            	throw new AuthenticationException("Token已失效！");
            }
    	}
    }


	@Override
    protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principals) {
        return new SimpleAuthorizationInfo();
    }

}
