/**
 * Copyright &copy; 2012-2016 <a href="https://github.com/thinkgem/jeesite">JeeSite</a> All rights reserved.
 */
package com.thinkgem.jeesite.modules.bas.service;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import cn.hutool.core.util.StrUtil;
import com.thinkgem.jeesite.common.utils.BlockChainUtils;
import com.thinkgem.jeesite.common.utils.DateUtils;
import com.thinkgem.jeesite.common.utils.IdGen;
import com.thinkgem.jeesite.common.utils.PropertyEnum.InspectionSituationEnum;
import com.thinkgem.jeesite.common.utils.SXRequest;
import com.thinkgem.jeesite.common.utils.SXSignUtil;
import com.thinkgem.jeesite.modules.bas.dao.CertificateProductInspectionDao;
import com.thinkgem.jeesite.modules.bas.entity.*;
import com.thinkgem.jeesite.modules.bas.utils.SerialNumberUtils;
import com.thinkgem.jeesite.modules.sys.entity.Attachment;
import com.thinkgem.jeesite.modules.sys.service.AttachmentService;
import com.thinkgem.jeesite.modules.sys.utils.UserUtils;

import cn.hutool.json.JSONUtil;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.thinkgem.jeesite.common.config.Config;
import com.thinkgem.jeesite.common.enumeration.EnumProperty;
import com.thinkgem.jeesite.common.persistence.Page;
import com.thinkgem.jeesite.common.service.CrudService;
import com.thinkgem.jeesite.modules.bas.dao.CertificateDao;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

/**
 * 合格证信息Service
 * <AUTHOR>
 * @version 2020-06-17
 */
@Service
@Transactional(readOnly = true)
public class CertificateService extends CrudService<CertificateDao, Certificate> {
	@Lazy
	@Autowired
	private EntService entService;

	@Lazy
	@Autowired
	private ProductService productService;

	@Lazy
	@Autowired
	private AttachmentService attachmentService;

	@Lazy
	@Autowired
	private CertificateService certificateService;

	@Lazy
	@Autowired
	private CertificateNoService certificateNoService;

	@Lazy
	@Autowired
	private ProductSampleService productSampleService;

	@Lazy
	@Autowired
	private CertificateProductInspectionDao certificateProductInspectionDao;
	@Autowired
	private AutographService autographService;

	@Override
	public Certificate get(String id) {
		List<String> stringList = certificateProductInspectionDao.getProductInspectionByCertificateId(id);
		Certificate certificate = super.get(id);
		if(certificate == null){
			return null;
		}
		if(!stringList.isEmpty()){
			certificate.setInspectionSituationList(stringList);
		}
		if(StringUtils.isNotEmpty(certificate.getProductId())){
			Product product = productService.get(certificate.getProductId());
			certificate.setProduct(product);
		}

		return certificate;
	}

	/**
	 * 电子证展示-查询接口
	 * @param id
	 * @return
	 */
	public Certificate getForElectronicShow(String id) {
		Certificate certificate = get(id);
		if (certificate == null) {
			return null;
		}
		Attachment attachment = new Attachment();
		attachment.setTableId(certificate.getEntId());
		attachment.setFileType("sealPic");
		List<Attachment> sealPicList = attachmentService.findList(attachment);
		certificate.setSealPicList(sealPicList);
		certificate.setAutograph(autographService.getByEntId(certificate.getEntId()));

		List<CertificateNo> certificateNoList=certificateNoService.getFirstByCertificateId(id);
		if(!certificateNoList.isEmpty()) {
			certificate.setCertificateNoList(certificateNoList);
		}
		return certificate;
	}

	@Override
	public List<Certificate> findList(Certificate certificate) {
		return super.findList(certificate);
	}

	@Override
	public Page<Certificate> findPage(Page<Certificate> page, Certificate certificate) {
		// 拼接 机构与地区编码绑定查询 2021-06-07 胡志国添加
		dataScopeFilter(certificate, "areaWhere" , "a.ent_county");
		return super.findPage(page, certificate);
	}

	@Override
	@Transactional(readOnly = false,rollbackFor=Exception.class)
	public void save(Certificate certificate) {
		super.save(certificate);
	}

	/**
	 * @Title updatePrintCount
	 * @Description 更新打印数量
	 * <AUTHOR>
	 * @Date 2020/7/21 17:00
	 * @Param id
	 * @Param printCountNew
	 * @Return void
	 **/
	@Transactional(readOnly = false,rollbackFor=Exception.class)
	public void updatePrintCount(String id, String printCountNew) {
		Certificate certificate = new Certificate();
		certificate.setId(id);
		certificate.setPrintCount(printCountNew);
		certificate.preUpdate();
		dao.updatePrintCount(certificate);
		TransactionSynchronizationManager.registerSynchronization( //在一个有事务的方法中，等事务提交后调另外一个方法可以用
				new TransactionSynchronizationAdapter() {
					@Override
					public void afterCommit() {
						// 关联区块链 必须使用 certificateService
						certificateService.joinBlockChain(certificate.getId());
					}
				}
		);
	}

	/**
	 * @Title insertAll
	 * @Description 保存合格证数据，冗余产品、企业信息
	 * <AUTHOR>
	 * @Date 2020/7/20 17:01
	 * @Param certificate
	 * @Return void
	 **/
	@Transactional(readOnly = false,rollbackFor=Exception.class)
	public void insertAll(Certificate certificate) {
		Ent ent = entService.getByTableId(UserUtils.getPrincipal().getId());
		Product product = productService.get(certificate.getProductId());
		if(!InspectionSituationEnum.IS_02.getKey().equals(certificate.getInspectionSituation())) {
			product.setCurrentSampleNo(null);
		}
		certificate.setEnt(ent);
		certificate.setProduct(product);
		// 批次、流水号生成
		//this.batchNoGeneration(certificate,ent,product);
		/**
		 * 使用新流水号生成规则
		 * 2020年8月4日15:16:58
		 * lxy
		 */
		this.batchGenerationSerialNumber(certificate,ent,product);
		certificate.preInsert();
		dao.insertAll(certificate);
		/**
		 * 更新主体合格证开具信息
		 * 2021年1月13日16:36:06
		 * lxy
		 */
		this.entService.updateCertificateAmount(ent.getId(), Integer.valueOf(certificate.getPrintCount()));

		/**
		 * 更新产品打印数量信息
		 * 2022年1月10日14:31:08
		 * lxy
		 */
		this.productService.updatePrintAmount(certificate.getProductId(), Integer.valueOf(certificate.getPrintCount()));
		// 企业、产品相关附件与合格证关联
		attachmentService.copyAttachment(ent.getId(),"bas_ent",certificate.getId(),"bas_certificate");
		attachmentService.copyAttachment(product.getId(),"bas_product",certificate.getId(),"bas_certificate");
		/*TransactionSynchronizationManager.registerSynchronization( //在一个有事务的方法中，等事务提交后调另外一个方法可以用
				new TransactionSynchronizationAdapter() {
					@Override
					public void afterCommit() {
						// 关联区块链 必须使用 certificateService
						certificateService.joinBlockChain(certificate.getId());
					}
				}
		);*/
	}

	/**
	 * @Title insertAll
	 * @Description 保存合格证数据，冗余产品、企业信息
	 * <AUTHOR>
	 * @Date 2020/7/24 11:38
	 * @Param certificate
	 * @Return void
	 **/
	@Transactional(readOnly = false,rollbackFor=Exception.class)
	public void insertAllForApp(Certificate certificate) {
		Ent ent = new Ent();
		if(null == UserUtils.getPrincipal()){
			 ent = entService.getByTableId(certificate.getTableId());
		}else {
			 ent = entService.getByTableId(UserUtils.getPrincipal().getId());
		}
		Product product = productService.get(certificate.getProductId());
		certificate.setEnt(ent);
		certificate.setProduct(product);

		List<String> InspectionSituationList = certificate.getInspectionSituationList();
		List<CertificateProductInspection> certificateProductInspectionList = new ArrayList<>();
		if(InspectionSituationList!=null){
			for(String inspectionSituation :InspectionSituationList){
				if(!InspectionSituationEnum.IS_02.getKey().equals(inspectionSituation)) {
					product.setCurrentSampleNo(null);
					//非快检获取该产品最新的检测结果数据
					ProductSample productSample=productSampleService.getProductSample(ent.getId(),product.getId(),inspectionSituation);
					if(null!=productSample) {
						CertificateProductInspection certificateProductInspection = new CertificateProductInspection();
						certificateProductInspection.setProductInspectionId(productSample.getProductInspectionId());
						certificateProductInspectionList.add(certificateProductInspection);
					}
				}
			}
		}


//		if(!InspectionSituationEnum.IS_02.getKey().equals(certificate.getInspectionSituation())) {
//			product.setCurrentSampleNo(null);
//			//非快检获取该产品最新的检测结果数据
//			ProductSample productSample=productSampleService.getProductSample(ent.getId(),product.getId(),certificate.getInspectionSituation());
//			if(null!=productSample) {
//				certificate.setProductInspectionId(productSample.getProductInspectionId());
//			}
//		}
		// 批次、流水号生成
		//this.batchNoGeneration(certificate,ent,product);
		/**
		 * 使用新流水号生成规则
		 * 2020年8月4日15:16:58
		 * lxy
		 */
		this.batchGenerationSerialNumber(certificate,ent,product);
		certificate.preInsert();
		dao.insertAll(certificate);
		for(CertificateProductInspection certificateProductInspection:certificateProductInspectionList){
			certificateProductInspection.setCertificateId(certificate.getId());
			certificateProductInspection.preInsert();
		}
		certificateProductInspectionDao.insertBatch(certificateProductInspectionList);

		/**
		 * 更新主体合格证开具信息
		 * 2021年1月13日16:36:06
		 * lxy
		 */
		this.entService.updateCertificateAmount(ent.getId(), Integer.valueOf(certificate.getPrintCount()));
		/**
		 * 更新产品打印数量信息
		 * 2022年1月10日14:31:08
		 * lxy
		 */
		this.productService.updatePrintAmount(certificate.getProductId(), Integer.valueOf(certificate.getPrintCount()));

		// 企业、产品相关附件与合格证关联
		attachmentService.copyAttachment(ent.getId(),"bas_ent",certificate.getId(),"bas_certificate");
		attachmentService.copyAttachment(product.getId(),"bas_product",certificate.getId(),"bas_certificate");

		/*TransactionSynchronizationManager.registerSynchronization( //在一个有事务的方法中，等事务提交后调另外一个方法可以用
				new TransactionSynchronizationAdapter() {
					@Override
					public void afterCommit() {
						// 关联区块链 必须使用 certificateService
						certificateService.joinBlockChain(certificate.getId());
					}
				}
		);*/
	}

	@Transactional(readOnly = false,rollbackFor=Exception.class)
	public Certificate printAgainForApp(Certificate certificate) {
		Certificate c = super.get(certificate.getId());
		Ent ent = new Ent();
		if(null == UserUtils.getPrincipal()){
			ent = entService.getByTableId(c.getTableId());
		}else {
			ent = entService.getByTableId(UserUtils.getPrincipal().getId());
		}

		Product product = productService.get(c.getProductId());
		c.setEnt(ent);
		c.setProduct(product);

		String printCount = StrUtil.toString(Integer.parseInt(certificate.getPrintCount()) + Integer.parseInt(c.getPrintCount()));

		c.setPrintCount(certificate.getPrintCount());
		this.batchGenerationSerialNumber(c);

		updatePrintCount(c.getId(), printCount);

		/**
		 * 更新主体合格证开具次数
		 * 2021年1月13日16:36:06
		 * lxy
		 */
		this.entService.updateCertificatePrintAmount(ent.getId(), Integer.valueOf(certificate.getPrintCount()));
		/**
		 * 更新产品打印数量信息
		 * 2022年1月10日14:31:08
		 * lxy
		 */
		this.productService.updatePrintAmount(certificate.getProductId(), Integer.valueOf(certificate.getPrintCount()));
		List<String> stringList = certificateProductInspectionDao.getProductInspectionByCertificateId(c.getId());
		if(!stringList.isEmpty()){
			c.setInspectionSituationList(stringList);
		}
		return c;
	}


	@Override
	@Transactional(readOnly = false,rollbackFor=Exception.class)
	public void delete(Certificate certificate) {
		super.delete(certificate);
	}

	/**
	 *@title: searchNextNo
	 *@author: CaoBochun
	 *@date: 2020年06月18日 06时02分16秒
	 *@description: 查询最大编号的下一个
	 *@param: []
	 *@return: {@link String}
	 */
	public String searchNextNo(String code){
		//补位
		DecimalFormat df=new DecimalFormat("00000");
		return df.format(this.dao.searchNextNo(code));
	}

	/**
	 *
	* @Title: getFrequencyByEntId
	* @author: LJX
	* @date: 2020年7月19日 下午2:56:44
	* @Description: 根据企业ID获取打印合格证次数和数量
	* @param:  ent
	* @return: Map<String,Object>
	* @throws
	 */
	public Map<String, Object> getFrequencyByEntId(Ent ent) {
		return dao.getFrequencyByEntId(ent);
	}

	/**
	 *
	* @Title: getFrequencyByCountyCode
	* @author: LJX
	* @date: 2020年7月19日 下午6:05:02
	* @Description:  根据企业县区code获取打印合格证次数和数量
	* @param:  county
	* @return: Map<String,Object>
	* @throws
	 */
	public Map<String,Object> getFrequencyByCountyCode(String county) {
		Certificate certificate = new Certificate();
		certificate.setEntCounty(county);
		// 拼接 机构与地区编码绑定查询 2021-06-07 胡志国添加
		dataScopeFilter(certificate, "areaWhere" , "a.ent_county");
		Map<String,Object> map = dao.getFrequencyByCountyCode(certificate);
		map.put("certificatePrintCountAmount",Double.valueOf(map.get("certificatePrintCountAmount").toString()).intValue());
		return map;
	}

	/**
	 * @Title batchNoGeneration
	 * @Description 根据主体所在区域生成当天批次号
	 * <AUTHOR>
	 * @Date 2020/7/22 9:04
	 * @Param code
	 * @Return java.lang.String
	 **/
	private void batchNoGeneration(Certificate certificate,Ent ent,Product product){
		String code = ent.getCounty(); // 主体所在区域
		String businessType = ent.getBusinessType(); // 主体类型
		String entType = ent.getEntType(); // 主体性质
		String productSortCode = product.getProductSortCode(); // 产品分类
		DecimalFormat df=new DecimalFormat("00");
		Calendar cal = Calendar.getInstance();
		String day = df.format(cal.get(Calendar.DATE));
		String month = df.format(cal.get(Calendar.MONTH) + 1);
		String year = String.valueOf(cal.get(Calendar.YEAR)).substring(2);
		String no = this.searchNextNo(code); // 当天主体所在区域最大流水号+1

		String batchNo = code + businessType + entType + productSortCode + year + month + day + no;
		certificate.setNo(no);
		certificate.setBatchNo(batchNo);
	}
	/**
	 *
	* @title: batchGenerationSerialNumber
	* @author: lxy
	* @date: 2020年8月4日 下午2:45:38
	* @description: 批量生成流水号
	* @param: @param certificate
	* @param: @param ent
	* @param: @param product
	* @return: void
	 */
	@Transactional(readOnly = false,rollbackFor=Exception.class)
	public void batchGenerationSerialNumber(Certificate certificate,Ent ent,Product product) {
		certificate.setIsNewRecord(true);
		certificate.setId(IdGen.nextId());
		//1-6位主体所在区域
		String code = ent.getCounty();
		//第7位主体类型
		String businessType = ent.getBusinessType();
		//第8位主体性质
		String entType = ent.getEntType();
		//第9-10位产品分类
		String productSortCode = product.getProductSortCode();
		//第11-16位年月日
		String date=DateUtils.getDate("yyMMdd");
		//第17-23位  流水号
		String batchNo=code+businessType+entType+productSortCode+date;
		certificate.setBatchNo(batchNo);
		batchGenerationSerialNumber(certificate);
	}

	@Transactional(readOnly = false,rollbackFor=Exception.class)
	public void batchGenerationSerialNumber(Certificate certificate) {
		String batchNo = certificate.getBatchNo();
		//批量保存流水号
		Integer printCount=Integer.parseInt(certificate.getPrintCount());
		List<CertificateNo> certificateNoList=new ArrayList<CertificateNo>();
		for(int i=0;i<printCount;i++) {
			Integer serialNumber= SerialNumberUtils.getInstance().geneterNextNumber(batchNo);
			//完整合格证号
			String fullNumber=batchNo+String.format("%0"+Config.getSerialNumberLength()+"d", serialNumber);
			CertificateNo certificateNo=new CertificateNo(certificate.getId(), batchNo, serialNumber, fullNumber);
			//由于跳号问题，如再次开具，暂不更新范围字段 --by Wlq 20250115
			if (certificate.getIsNewRecord()) {
				//主表记录开始、截止流水号
				if(i==0) {
					//首位流水号
					certificate.setBeginSerialNumber(fullNumber);
					//末尾流水号
					certificate.setEndSerialNumber(fullNumber);
				}else if(i==printCount-1) {
					//末尾流水号
					certificate.setEndSerialNumber(fullNumber);
				}
			}
			certificateNo.preInsert();
			certificateNoList.add(certificateNo);
		}

		if(!certificateNoList.isEmpty()) {
			this.certificateNoService.insertBatch(certificateNoList);
			if(Config.isBlockChainEnabled()) {
				TransactionSynchronizationManager.registerSynchronization( //在一个有事务的方法中，等事务提交后调另外一个方法可以用
						new TransactionSynchronizationAdapter() {
							@Override
							public void afterCommit() {
								// 关联区块链 必须使用 certificateService
								certificateService.joinBlockChainCertificateNo(certificate.getId(), certificateNoList);
							}
						}
				);
			}
			/**
			 * 数据中心推送
			 * lxy
			 * 2021年7月12日8:47:06
			 */
			if(Config.isDataCentrePushEnabled()) {
				/**
				 * 按允许推送区域推送数据
				 * lxy
				 * 2021年8月3日16:17:21
				 */
				if(Config.getDataCentrePushAreaCode().equals(certificate.getEnt().getCity())) {
					dataCentrePush(certificateNoList, certificate);
				}
			}
			certificate.setCertificateNoList(certificateNoList);
		}
	}
	/**
	 *
	* @Title: getSummaryInfo
	* @author: lxy
	* @date: 2020年7月19日 下午6:08:00
	* @Description:获取汇总信息
	* @param:  county
	* @return: Map<String,Object>
	* @throws
	 */
	public Map<String, Object> getSummaryInfo(String entId){
		return dao.getSummaryInfo(entId);
	}


	/**
	 * @Title joinBlockChain
	 * @Description //TODO 合格证绑定区块链，暂时只要数据更新就重新生成一个区块链id
	 * <AUTHOR>
	 * @Date 2020/7/26 19:10
	 * @Param id
	 * @Return void
	 **/
	@Async
	@Transactional(readOnly = false)
	public void joinBlockChain(String id) {
		Certificate certificate = certificateService.get(id);
		Map<String, Object> resultMap = new HashMap<>();
		String blockchainId = "";
		String msg ="";
		try{
			if (StringUtils.isNotBlank(id)){
				Map<String, Object> map = new HashMap<>();
				// 暂时只要数据更新就重新生成一个区块链id
				/*blockchainId = certificate.getBlockChainId();
				if(StringUtils.isNotBlank(blockchainId)){
					map.put("_id",blockchainId); // 区块链id
				}*/
				map.put("id",certificate.getId()); // 合格证主键
				map.put("batchNo",certificate.getBatchNo()); // 合格证编号
				map.put("certificateIssuingDate", DateUtils.formatDateTime(certificate.getCreateDate())); //合格证开具时间
				map.put("productId",certificate.getProductId()); // 产品id
				map.put("productName",certificate.getProductName()); // 产品名称
				map.put("productIntroduction",certificate.getProductIntroduction()); // 产品介绍
				map.put("productProvince", certificate.getProductProvince()); // 产品生产-省份code
				map.put("productCity", certificate.getProductCity()); // 产品生产-城市code
				map.put("productCounty", certificate.getProductCounty()); // 产品生产-县区code
				map.put("productAddress", certificate.getProductAddress()); // 产品生产所在区域
				map.put("productDetail",certificate.getProductDetail()); // 产品生产地址
				map.put("productSortName",certificate.getProductSortName()); // 产品类别
				map.put("productCertificationName",certificate.getProductCertificationName()); // 产品认证名称
				map.put("productNum",certificate.getProductNum()); // 产品数量/重量
				map.put("productUnitName",certificate.getProductUnitName()); // 产品单位（斤、个等）
				map.put("productionDate",DateUtils.formatDateTime(certificate.getProductionDate())); // 产品生产日期
				map.put("entId",certificate.getEntId()); // 产品生产单位
				map.put("entBusinessType",certificate.getEntBusinessType()); // 主体类型
				map.put("entType",certificate.getEntType()); // 主体性质
				map.put("entDetail",certificate.getEntDetail()); // 主体详细地址
				map.put("entSocialCode",certificate.getEntSocialCode()); // 主体-统一社会信用代码
				map.put("entContactsPhone",certificate.getEntContactsPhone()); // 主体-联系电话
				map.put("entCompanyIntroduction",certificate.getEntCompanyIntroduction()); // 主体简介
				map.put("entHonor",certificate.getEntHonor()); // 主体荣誉
				map.put("printCount",certificate.getPrintCount()); // 打印数量
				map.put("createDate",DateUtils.formatDateTime(certificate.getCreateDate())); // 数据创建时间
				map.put("updateDate",DateUtils.formatDateTime(certificate.getUpdateDate())); // 数据更新时间
				map.put("inspectionSituation", certificate.getInspectionSituation()); // 检测情况
		        map.put("sampleNo", certificate.getSampleNo()); // 当前产品样品编号
				//ResponseEntity<Map> responseEntity = BlockChainUtils.checkById("b72077ddee491976953251c1736bcbc9b7863ef03e1dd02c120d82361cbb4759");
				ResponseEntity<Map> responseEntity = BlockChainUtils.insertData(map);
				resultMap = responseEntity.getBody();
				if(responseEntity == null || responseEntity.getStatusCode() != HttpStatus.OK){
					logger.info("【插入失败：区块链】 ID为：{} 的追溯数据插入失败。失败原因：{}",id,resultMap.get("message").toString());
					msg ="失败";
					//throw new RuntimeException(msg);
				}

				if(resultMap.get("code").equals(200)){
					if(StringUtils.isBlank(blockchainId)) {
						blockchainId = ((Map<String, Object>) resultMap.get("data")).get("txId").toString();
						//给当前追溯数据添加区块链ID
						certificateService.updateBlockChainId(id, blockchainId);
					}
				}else{
					//throw new RuntimeException(resultMap.get("message").toString());
				}

				logger.info("【插入成功】 ID为：{} 的追溯数据插入成功。区块链ID为：{}",id,blockchainId);
			}
		}catch (Exception e){
			e.printStackTrace();
			logger.info("【插入失败】 ID为：{} 的追溯数据插入失败。失败原因：{}",id,e.getMessage());
			msg ="失败";
			// 确保数据回滚
			//throw new RuntimeException(msg);
		}

	}
	/**
	 * @Title joinBlockChainCertificateNo
	 * @Description 合格证绑定区块链，暂时只要数据更新就重新生成一个区块链id
	 * <AUTHOR>
	 * @Date 2020年8月5日14:41:15
	 * @Param id
	 * @Param list
	 * @Return void
	 **/
	@Async
	@Transactional(readOnly = false)
	public void joinBlockChainCertificateNo(String id,List<CertificateNo> list) {
		Certificate certificate = certificateService.get(id);
		try{
			if (StringUtils.isNotBlank(id)){
				Map<String, Object> map = new HashMap<>();
				for(CertificateNo certificateNo:list) {
					String blockchainId = "";
					Map<String, Object> resultMap = new HashMap<>();
					map.put("id",certificateNo.getId()); // 合格证流水主键
					map.put("certificateId",certificateNo.getCertificateId()); // 合格证主键
					map.put("batchNo",certificateNo.getBatchNo()); // 合格证批次号
					map.put("fullNumber",certificateNo.getFullNumber()); // 合格证全编号
					map.put("certificateIssuingDate", DateUtils.formatDateTime(certificate.getCreateDate())); //合格证开具时间

					map.put("productId",certificate.getProductId()); // 产品id
					map.put("productName",certificate.getProductName()); // 产品名称
					map.put("productIntroduction",certificate.getProductIntroduction()); // 产品介绍
					map.put("productProvince", certificate.getProductProvince()); // 产品生产-省份code
					map.put("productCity", certificate.getProductCity()); // 产品生产-城市code
					map.put("productCounty", certificate.getProductCounty()); // 产品生产-县区code
					map.put("productAddress", certificate.getProductAddress()); // 产品生产所在区域
					map.put("productDetail",certificate.getProductDetail()); // 产品生产地址
					map.put("productSortCode", certificate.getProductSortCode()); // 产品类别code
					map.put("productSortName",certificate.getProductSortName()); // 产品类别
					map.put("productCertificationName",certificate.getProductCertificationName()); // 产品认证名称
					map.put("productNum",certificate.getProductNum()); // 产品数量/重量
					map.put("productUnitCode", certificate.getProductUnitCode()); // 产品单位（斤、个等）code
					map.put("productUnitName",certificate.getProductUnitName()); // 产品单位（斤、个等）
					map.put("productionDate",DateUtils.formatDateTime(certificate.getProductionDate())); // 产品生产日期

					map.put("entId",certificate.getEntId()); // 产品生产单位
					map.put("entName", certificate.getEntName()); // 主体名称
					map.put("entBusinessType",certificate.getEntBusinessType()); // 主体类型
					map.put("entType",certificate.getEntType()); // 主体性质
					map.put("entMainType",certificate.getEntMainType()); // 主体类别
					map.put("entFarmType",certificate.getEntFarmType()); // 养殖分类 (0:牧业 1:渔业)
					map.put("entCardNo", certificate.getEntCardNo()); // 身份证号
					map.put("entLegalPerson", certificate.getEntLegalPerson()); // 法人
					map.put("entAddress", certificate.getEntAddress()); // 主体所在区域
					map.put("entDetail",certificate.getEntDetail()); // 主体详细地址
					map.put("entSocialCode",certificate.getEntSocialCode()); // 主体-统一社会信用代码
					map.put("entContactsPhone",certificate.getEntContactsPhone()); // 主体-联系电话
					map.put("entAutograph", certificate.getEntAutograph()); // 签名base64
					map.put("entCompanyIntroduction",certificate.getEntCompanyIntroduction()); // 主体简介
					map.put("entHonor",certificate.getEntHonor()); // 主体荣誉
					map.put("printCount",certificateNo.getPrintCount()); // 打印数量 默认1 补打业务后 需要调整
					map.put("createDate",DateUtils.formatDateTime(certificateNo.getCreateDate())); // 数据创建时间
					map.put("updateDate",DateUtils.formatDateTime(certificateNo.getUpdateDate())); // 数据更新时间
					map.put("inspectionSituation", certificate.getInspectionSituation()); // 检测情况
			        map.put("sampleNo", certificate.getSampleNo()); // 当前产品样品编号
					ResponseEntity<Map> responseEntity = BlockChainUtils.insertData(map);
					resultMap = responseEntity.getBody();
					if(responseEntity == null || responseEntity.getStatusCode() != HttpStatus.OK){
						logger.info("【插入失败：区块链】 ID为：{} 的追溯数据插入失败。失败原因：{}",id,resultMap.get("message").toString());
						//throw new RuntimeException(msg);
					}

					if(resultMap.get("code").equals(200)){
						if(StringUtils.isBlank(blockchainId)) {
							blockchainId = ((Map<String, Object>) resultMap.get("data")).get("txId").toString();
							//给当前追溯数据添加区块链ID
							certificateNoService.updateBlockChainId(certificateNo.getId(),blockchainId);
						}
					}else{
						//throw new RuntimeException(resultMap.get("message").toString());
					}

					logger.info("【插入成功】 ID为：{} 的追溯数据插入成功。区块链ID为：{}",id,blockchainId);
				}
			}
		}catch (Exception e){
			e.printStackTrace();
			logger.info("【插入失败】 ID为：{} 的追溯数据插入失败。失败原因：{}",id,e.getMessage());
			// 确保数据回滚
			//throw new RuntimeException(msg);
		}

	}

	/**
	 * @Title updateBlockchainId
	 * @Description //TODO 为合格证绑定区块链id
	 * <AUTHOR>
	 * @Date 2020/7/26 16:23
	 * @Param id
	 * @Param blockchainId
	 * @Return void
	 **/
	public void updateBlockChainId(String id,String blockChainId){
		dao.updateBlockChainId(id,blockChainId);
	}
	/**
	 *
	* @title: getCertificateNo
	* @author: lxy
	* @date: 2020年8月4日 下午5:16:34
	* @description: 获取并带流水数据集合
	* @param: id
	* @return: Certificate
	 */
	public Certificate getCertificateNo(String id) {
		List<String> stringList = certificateProductInspectionDao.getProductInspectionByCertificateId(id);
		Certificate certificate= super.get(id);
		if(!stringList.isEmpty()){
			certificate.setInspectionSituationList(stringList);
		}

		/**
		 * 流水数据集合赋值
		 * 2020年8月4日17:16:13
		 * lxy
		 */
		CertificateNo certificateNoQuery=new CertificateNo();
		certificateNoQuery.setCertificateId(id);
		List<CertificateNo> certificateNoList=certificateNoService.findList(certificateNoQuery);
		if(!certificateNoList.isEmpty()) {
			certificate.setCertificateNoList(certificateNoList);
		}
		return certificate;
	}
	/**
	 *
	* @title: findInvalidPage
	* @author: lxy
	* @date: 2021年5月13日 上午11:25:55
	* @description: 获取作废数据列表
	* @param: page
	* @param: certificate
	* @return: Page<Certificate>
	 */
	public Page<Certificate> findInvalidPage(Page<Certificate> page, Certificate certificate) {
		certificate.setPage(page);
		// 拼接 机构与地区编码绑定查询 2021-06-07 胡志国添加
		dataScopeFilter(certificate, "areaWhere" , "a.ent_county");
		page.setList(dao.findInvalidList(certificate));
		return page;
	}

	/**
	 *
	* @title: dataCentrePush
	* @author: lxy
	* @date: 2021年7月12日 上午11:15:20
	* @description: 数据中心推送
	* @param: @param certificateNoList
	* @param: @param certificate
	* @return: void
	 */
	@Transactional(readOnly = false,rollbackFor=Exception.class)
	public void dataCentrePush(List<CertificateNo> certificateNoList,Certificate certificate) {
		ExecutorService fixedThreadPool = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
		fixedThreadPool.execute(new Runnable() {
			public void run() {
				try {
					dataCentrePushDo(certificateNoList,certificate);
					Thread.sleep(100);
				} catch (InterruptedException e) {
					e.printStackTrace();
				}
			}
		});
		//手动关闭线程池
		fixedThreadPool.shutdown();
	}
	/**
	 *
	* @title: dataCentrePushDo
	* @author: lxy
	* @date: 2021年7月12日 上午11:41:17
	* @description: 数据中心推送实现
	* @param: @param certificateNoList
	* @param: @param certificate
	* @return: void
	 */
	@Transactional(readOnly = false,rollbackFor=Exception.class)
	public void dataCentrePushDo(List<CertificateNo> certificateNoList, Certificate certificate) {
		if (null != certificateNoList && !certificateNoList.isEmpty()) {
			Map<String, String> data = new HashMap<>();
			data.put(SXSignUtil.SXConstants.FIELD_NONCE_STR, SXSignUtil.generateNonceStr());
			data.put(SXSignUtil.SXConstants.FIELD_TIME_STAMP, SXSignUtil.getCurrentTimestampStr());
			data.put(SXSignUtil.SXConstants.FIELD_APPID, Config.getDataCentreAppId());
			data.put(SXSignUtil.SXConstants.FIELD_NOTIFY_URL, Config.getDataCentreNotifyUrl());
			for (CertificateNo item : certificateNoList) {
				data.put("id", IdGen.nextId());
				data.put("no", item.getFullNumber());
				data.put("branchNo", item.getBatchNo());
				data.put("nodeId", certificate.getEnt().getId());
				data.put("nodeName", certificate.getEnt().getName());
				String nodeType = "";
				if (EnumProperty.BusinessTypeEnum.TYPE_0.getKey().equals(certificate.getEnt().getBusinessType())) {// 种植
					nodeType = EnumProperty.NodeTypeEnum.TYPE_0.getKey();
				} else if (EnumProperty.BusinessTypeEnum.TYPE_1.getKey().equals(certificate.getEnt().getBusinessType())) {// 养殖
					nodeType = EnumProperty.NodeTypeEnum.TYPE_1.getKey();
				}
				data.put("nodeType", nodeType);
				data.put("dataId", item.getId());
				data.put("dataTime", DateUtils.formatDateTime(item.getCreateDate()));
				data.put("areaCode", certificate.getEnt().getCounty());
				data.put("address", certificate.getEnt().getDetail());
				data.put("nodeSource", "certificate");

				data.put("productName", certificate.getProductName());
				data.put("productId", certificate.getProductId());
				try {
					String sign = SXSignUtil.sign(data, Config.getDataCentreSecret());
					data.put(SXSignUtil.SXConstants.FIELD_SIGN, sign);
					String apiPath=Config.getDataCentreGateway()+Config.getDataCentreTraceApi();
					String response = SXRequest.requestOnce(apiPath, JSONUtil.toJsonStr(data),
							null);
					/*if (response.contains(PushFlagEnum.PUSH_1.getValue())) {
						// trace.setPushFlag(PushFlagEnum.PUSH_1.getKey());
						// this.traceService.update(trace);
					}*/
				} catch (Exception e) {

				}
			}
		}
	}

	/**
	 * @title:findCertificatePage
	 * @author:Lizhongyao
	 * @data:2021年09月11日 09:59:33
	 * @param:page
	 * @param:certificate
	 * @description 获取开具记录
	 * @return com.thinkgem.jeesite.common.persistence.Page<com.thinkgem.jeesite.modules.bas.entity.Certificate>
	 **/
	public Page<Certificate> findCertificatePage(Page<Certificate> page, Certificate certificate) {
		return super.findPage(page, certificate);
	}

	/**
	 * @title:findPrintTotalNum
	 * @author:Lizhongyao
	 * @data:2021年09月11日 09:59:33
	 * @param:certificate
	 * @description 获取开具数量
	 * @return java.lang.Integer
	 **/
	public Integer findPrintTotalNum(Certificate certificate) {
		return dao.findPrintTotalNum(certificate);
	}


	public int selectCertificateCount(Certificate certificate) {
		return dao.selectCertificateCount(certificate);
	}
}
