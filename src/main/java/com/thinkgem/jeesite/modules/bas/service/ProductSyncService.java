/**
 * Copyright &copy; 2012-2016 <a href="https://github.com/thinkgem/jeesite">JeeSite</a> All rights reserved.
 */
package com.thinkgem.jeesite.modules.bas.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.thinkgem.jeesite.common.config.Global;
import com.thinkgem.jeesite.common.utils.HttpClientUtil;
import com.thinkgem.jeesite.common.utils.StringUtils;
import com.thinkgem.jeesite.modules.bas.entity.Product;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Product定时增量同步服务
 * <AUTHOR>
 * @version 2024-01-01
 */
@Service
@Lazy(false)
public class ProductSyncService {

    private static final Logger logger = LoggerFactory.getLogger(ProductSyncService.class);
    
    @Autowired
    private ProductService productService;
    
    /**
     * 定时同步任务 - 每分钟执行一次
     * cron表达式：秒 分 时 日 月 周
     */
    @Scheduled(cron = "0 */1 * * * ?")
    public void syncProductData() {
        // 检查同步功能是否启用
        String syncEnabled = Global.getConfig("product.sync.enabled");
        if (!"true".equals(syncEnabled)) {
            logger.debug("Product同步功能已禁用，跳过本次同步");
            return;
        }
        
        logger.info("=== Product定时增量同步开始 ===");
        Date syncStartTime = new Date();
        Date currentSyncTime = new Date(); // 本次同步的基准时间
        
        int totalCount = 0;
        int successCount = 0;
        int failCount = 0;
        
        try {
            // 1. 获取上次同步时间
            Date lastSyncTime = productService.getMaxSyncDate();
            logger.info("上次同步时间: {}", lastSyncTime != null ? formatDate(lastSyncTime) : "无记录");
            
            // 2. 调用外部接口获取增量数据
            List<Product> productList = fetchIncrementalData(lastSyncTime);
            totalCount = productList != null ? productList.size() : 0;
            logger.info("从外部接口获取到 {} 条产品数据", totalCount);
            
            if (totalCount > 0) {
                // 3. 批量处理数据
                successCount = processBatchData(productList, currentSyncTime);
                failCount = totalCount - successCount;
            }
            
        } catch (Exception e) {
            logger.error("Product同步过程中发生异常", e);
            failCount = totalCount;
        } finally {
            // 记录同步结果
            Date syncEndTime = new Date();
            long duration = syncEndTime.getTime() - syncStartTime.getTime();
            logger.info("=== Product定时增量同步结束 ===");
            logger.info("同步耗时: {}ms, 总数据量: {}, 成功: {}, 失败: {}", 
                       duration, totalCount, successCount, failCount);
        }
    }
    
    /**
     * 从外部接口获取增量数据
     * @param lastSyncTime 上次同步时间
     * @return 产品列表
     */
    private List<Product> fetchIncrementalData(Date lastSyncTime) {
        List<Product> productList = new ArrayList<>();
        
        try {
            // 构建请求参数
            Map<String, String> params = new HashMap<>();
            if (lastSyncTime != null) {
                String timeFormat = Global.getConfig("product.sync.time.format");
                if (StringUtils.isBlank(timeFormat)) {
                    timeFormat = "yyyy-MM-dd HH:mm:ss";
                }
                SimpleDateFormat sdf = new SimpleDateFormat(timeFormat);
                params.put("lastSyncTime", sdf.format(lastSyncTime));
            }
            
            // 获取接口配置
            String apiUrl = Global.getConfig("product.sync.api.url");
            String timeoutStr = Global.getConfig("product.sync.api.timeout");
            int timeout = StringUtils.isNotBlank(timeoutStr) ? Integer.parseInt(timeoutStr) : 30000;
            
            logger.info("调用外部接口: {}, 参数: {}", apiUrl, params);
            
            // 调用外部接口
            String response = HttpClientUtil.doGet(apiUrl, params);
            logger.debug("外部接口响应: {}", response);
            
            if (StringUtils.isNotBlank(response)) {
                // 解析响应数据
                productList = parseResponseData(response);
            }
            
        } catch (Exception e) {
            logger.error("调用外部接口获取数据失败", e);
        }
        
        return productList;
    }
    
    /**
     * 解析外部接口响应数据
     * @param response 响应字符串
     * @return 产品列表
     */
    private List<Product> parseResponseData(String response) {
        List<Product> productList = new ArrayList<>();
        
        try {
            JSONObject jsonResponse = JSON.parseObject(response);
            String code = jsonResponse.getString("code");
            
            if ("1".equals(code)) {
                Object dataObj = jsonResponse.get("data");
                
                if (dataObj instanceof JSONArray) {
                    JSONArray dataArray = (JSONArray) dataObj;
                    for (int i = 0; i < dataArray.size(); i++) {
                        JSONObject productJson = dataArray.getJSONObject(i);
                        Product product = convertJsonToProduct(productJson);
                        if (product != null) {
                            productList.add(product);
                        }
                    }
                } else if (dataObj instanceof String) {
                    // 如果data是字符串，尝试解析为JSON数组
                    String dataStr = (String) dataObj;
                    if (StringUtils.isNotBlank(dataStr)) {
                        JSONArray dataArray = JSON.parseArray(dataStr);
                        for (int i = 0; i < dataArray.size(); i++) {
                            JSONObject productJson = dataArray.getJSONObject(i);
                            Product product = convertJsonToProduct(productJson);
                            if (product != null) {
                                productList.add(product);
                            }
                        }
                    }
                }
            } else {
                String msg = jsonResponse.getString("msg");
                logger.warn("外部接口返回错误: code={}, msg={}", code, msg);
            }
            
        } catch (Exception e) {
            logger.error("解析外部接口响应数据失败", e);
        }
        
        return productList;
    }
    
    /**
     * 将JSON对象转换为Product实体
     * @param productJson JSON对象
     * @return Product实体
     */
    private Product convertJsonToProduct(JSONObject productJson) {
        try {
            Product product = new Product();
            
            // 基础字段映射
            product.setId(productJson.getString("id"));
            product.setName(productJson.getString("name"));
            product.setEntId(productJson.getString("entId"));
            product.setProductSortCode(productJson.getString("productSortCode"));
            product.setProductSortName(productJson.getString("productSortName"));
            product.setProductCertificationCode(productJson.getString("productCertificationCode"));
            product.setProductCertificationName(productJson.getString("productCertificationName"));
            
            // 地址相关字段
            product.setProvince(productJson.getString("province"));
            product.setCity(productJson.getString("city"));
            product.setCounty(productJson.getString("county"));
            product.setAddress(productJson.getString("address"));
            product.setDetail(productJson.getString("detail"));
            product.setLongitude(productJson.getString("longitude"));
            product.setLatitude(productJson.getString("latitude"));
            
            // 其他字段
            product.setProductIntroduction(productJson.getString("productIntroduction"));
            product.setUserId(productJson.getString("userId"));
            product.setDataScope(productJson.getString("dataScope"));
            
            // 时间字段处理
            String createTimeStr = productJson.getString("createTime");
            if (StringUtils.isNotBlank(createTimeStr)) {
                product.setCreateDate(parseDate(createTimeStr));
            }
            
            String updateTimeStr = productJson.getString("updateTime");
            if (StringUtils.isNotBlank(updateTimeStr)) {
                product.setUpdateDate(parseDate(updateTimeStr));
            }
            
            String addDateStr = productJson.getString("addDate");
            if (StringUtils.isNotBlank(addDateStr)) {
                product.setAddDate(parseDate(addDateStr));
            }
            
            // 创建者和更新者处理
            product.setCreateBy(createUserFromString(productJson.getString("createBy")));
            product.setUpdateBy(createUserFromString(productJson.getString("updateBy")));
            
            // 删除标识转换
            String externalDelFlag = productJson.getString("delFlag");
            product.setDelFlag(productService.convertDelFlag(externalDelFlag));
            
            // 备注
            product.setRemarks(productJson.getString("remarks"));
            
            return product;
            
        } catch (Exception e) {
            logger.error("转换JSON到Product实体失败: {}", productJson, e);
            return null;
        }
    }
    
    /**
     * 批量处理数据
     * @param productList 产品列表
     * @param currentSyncTime 当前同步时间
     * @return 成功处理的数量
     */
    private int processBatchData(List<Product> productList, Date currentSyncTime) {
        int successCount = 0;
        String batchSizeStr = Global.getConfig("product.sync.batch.size");
        int batchSize = StringUtils.isNotBlank(batchSizeStr) ? Integer.parseInt(batchSizeStr) : 100;
        
        // 分批处理数据
        for (int i = 0; i < productList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, productList.size());
            List<Product> batchList = productList.subList(i, endIndex);
            
            try {
                int batchResult = productService.batchSaveOrUpdateForSync(batchList, currentSyncTime);
                successCount += batchResult;
                logger.info("批次处理完成: {}-{}, 成功: {}", i + 1, endIndex, batchResult);
                
            } catch (Exception e) {
                logger.error("批次处理失败: {}-{}", i + 1, endIndex, e);
                // 单条处理失败的数据
                successCount += processSingleData(batchList, currentSyncTime);
            }
        }
        
        return successCount;
    }
    
    /**
     * 单条处理数据（当批量处理失败时使用）
     * @param productList 产品列表
     * @param currentSyncTime 当前同步时间
     * @return 成功处理的数量
     */
    private int processSingleData(List<Product> productList, Date currentSyncTime) {
        int successCount = 0;
        
        for (Product product : productList) {
            try {
                List<Product> singleList = Arrays.asList(product);
                int result = productService.batchSaveOrUpdateForSync(singleList, currentSyncTime);
                if (result > 0) {
                    successCount++;
                }
            } catch (Exception e) {
                logger.error("单条数据处理失败: id={}, name={}", product.getId(), product.getName(), e);
            }
        }
        
        return successCount;
    }
    
    /**
     * 解析日期字符串
     * @param dateStr 日期字符串
     * @return Date对象
     */
    private Date parseDate(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return sdf.parse(dateStr);
        } catch (Exception e) {
            logger.warn("日期解析失败: {}", dateStr);
            return null;
        }
    }
    
    /**
     * 从字符串创建用户对象
     * @param userStr 用户字符串
     * @return User对象
     */
    private com.thinkgem.jeesite.modules.sys.entity.User createUserFromString(String userStr) {
        if (StringUtils.isBlank(userStr)) {
            return null;
        }
        
        com.thinkgem.jeesite.modules.sys.entity.User user = 
            new com.thinkgem.jeesite.modules.sys.entity.User();
        user.setId(userStr);
        return user;
    }
    
    /**
     * 格式化日期
     * @param date 日期
     * @return 格式化后的字符串
     */
    private String formatDate(Date date) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }
}
