package com.thinkgem.jeesite.modules.bas.syncdataapi;

import com.thinkgem.jeesite.common.persistence.ResponseResult;
import com.thinkgem.jeesite.common.web.BaseController;
import com.thinkgem.jeesite.modules.bas.entity.*;
import com.thinkgem.jeesite.modules.bas.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = "${syncApiPath}/ent")
public class SyncEntApiController extends BaseController{

	@Autowired
	private EntService entService;

	@RequestMapping(value = "list", method = RequestMethod.POST)
	public ResponseResult list(@RequestBody Ent ent){
		return ResponseResult.success(entService.findList(ent));
	}

}
