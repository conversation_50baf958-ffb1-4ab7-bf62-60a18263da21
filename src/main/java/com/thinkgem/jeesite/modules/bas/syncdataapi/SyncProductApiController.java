package com.thinkgem.jeesite.modules.bas.syncdataapi;

import com.thinkgem.jeesite.common.persistence.ResponseResult;
import com.thinkgem.jeesite.common.web.BaseController;
import com.thinkgem.jeesite.modules.bas.entity.Ent;
import com.thinkgem.jeesite.modules.bas.entity.Product;
import com.thinkgem.jeesite.modules.bas.service.EntService;
import com.thinkgem.jeesite.modules.bas.service.ProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "${syncApiPath}/product")
public class SyncProductApiController extends BaseController{

	@Autowired
	private ProductService productService;

	@RequestMapping(value = "list", method = RequestMethod.POST)
	public ResponseResult list(@RequestBody Product product){
		return ResponseResult.success(productService.findList(product));
	}

}
