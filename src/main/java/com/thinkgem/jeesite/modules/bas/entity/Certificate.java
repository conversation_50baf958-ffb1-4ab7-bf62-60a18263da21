/**
 * Copyright &copy; 2012-2016 <a href="https://github.com/thinkgem/jeesite">JeeSite</a> All rights reserved.
 */
package com.thinkgem.jeesite.modules.bas.entity;

import com.thinkgem.jeesite.modules.sys.entity.Attachment;
import org.hibernate.validator.constraints.Length;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import com.thinkgem.jeesite.common.persistence.DataEntity;

/**
 * 合格证信息Entity
 * <AUTHOR>
 * @version 2020-06-17
 */
public class Certificate extends DataEntity<Certificate> {

	private static final long serialVersionUID = 1L;
	private String batchNo;		// 合格证批次号
	private String userId;		// 用户id （乡村版合格证用）
	private String dataScope;		// 数据来源（数据来源（0：正常合格证系统 1：乡村版合格证数据））
	private String no;  //流水号
	private String productId;		// 产品id
	private String productName;		// 产品名称
	private String productIntroduction;		// 产品介绍
	private String productProvince; // 产品生产-省份code
	private String productCity; // 产品生产-城市code
	private String productCounty; // 产品生产-县区code
	private String productAddress;		// 产品生产地址
	private String productDetail;		// 产品生产详细地址
	private String productSortCode;		// 分类code
	private String productSortName;		// 分类名称
	private String productCertificationCode; // 产品认证（有机农产品，绿色食品，无公害农产品，农产品地理标志）多选
	private String productCertificationName; // 产品认证名称
	private String productNum;		// 产品数量（重量）
	private String productUnitCode;		// 产品单位code
	private String productUnitName;		// 产品单位名称
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date productionDate;		// 生产日期
	private String printCount;		// 打印数量

	private String entId;//企业id
	private String entName; //企业名称
	private String entBusinessType; //主体类型(1:养殖；2：种植)
	private String entType; //主体性质(1:企业；2：个人)
	private String entMainType; //主体类别
	private String entFarmType;//养殖分类 (0:牧业 1:渔业)
	private String entCardNo; //身份证号
	private String entLegalPerson; //法人
	private String entProvince; //主体-省份code
	private String entCity; //主体-城市code
	private String entCounty; //主体-县区code
	private String entAddress; //地址(省市县)
	private String entDetail; //详细地址
	private String entSocialCode; //主体-统一社会信用代码
	private String entContactsPhone; //主体-联系电话
	private String entAutograph; //签名base64
	private String entCompanyIntroduction; //主体-简介
	private String entHonor;//荣誉简介

	private String blockChainId;//区块链id

	private String entCode; //主体行政区划
	private Date beginCreateDate;		// 开始 创建时间
	private Date endCreateDate;		// 结束 创建时间
	private String time;

	private Product product; //产品
	private Ent ent; // 实体
	private String sortName;//排序字段名称
	private String sortOrder;//字段排序
	private String beginSerialNumber;//开始流水号
	private String endSerialNumber;//结束流水号
	private String inspectionSituation;//检测情况
	private String sampleNo;//当前产品样品编号
	private String productInspectionId;//产品检测表id
	private String tableId;//ent 表tableID

	//辅助字段
	private List<CertificateNo> certificateNoList;
	private String searchType; //检索类型
	private String certificateNoId;//合格证流水id

	private List<Attachment> sealPicList; //盖章

	private Autograph autograph;//签字信息

	private List<String> inspectionSituationList; //检测类型集合

	private String reBuyVisible; //复购按钮是否显示1显示,其他不显示

	private Date syncDate;

	public Certificate() {
		super();
	}

	public Certificate(String id){
		super(id);
	}

	@Length(min=0, max=16, message="合格证批次号长度必须介于 0 和 16 之间")
	public String getBatchNo() {
		return batchNo;
	}

	public void setBatchNo(String batchNo) {
		this.batchNo = batchNo;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getDataScope() {
		return dataScope;
	}

	public void setDataScope(String dataScope) {
		this.dataScope = dataScope;
	}

	@Length(min=0, max=50, message="产品id长度必须介于 0 和 50 之间")
	public String getProductId() {
		return productId;
	}

	public void setProductId(String productId) {
		this.productId = productId;
	}

	@Length(min=0, max=50, message="产品名称长度必须介于 0 和 50 之间")
	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	@Length(min=0, max=50, message="分类code长度必须介于 0 和 50 之间")
	public String getProductSortCode() {
		return productSortCode;
	}

	public void setProductSortCode(String productSortCode) {
		this.productSortCode = productSortCode;
	}

	@Length(min=0, max=50, message="分类名称长度必须介于 0 和 50 之间")
	public String getProductSortName() {
		return productSortName;
	}

	public void setProductSortName(String productSortName) {
		this.productSortName = productSortName;
	}

	@Length(min=0, max=50, message="产品数量（重量）长度必须介于 1 和 50 之间")
	public String getProductNum() {
		return productNum;
	}

	public void setProductNum(String productNum) {
		this.productNum = productNum;
	}

	@Length(min=0, max=50, message="产品单位code长度必须介于 0 和 50 之间")
	public String getProductUnitCode() {
		return productUnitCode;
	}

	public void setProductUnitCode(String productUnitCode) {
		this.productUnitCode = productUnitCode;
	}

	@Length(min=0, max=50, message="产品单位名称长度必须介于 0 和 50 之间")
	public String getProductUnitName() {
		return productUnitName;
	}

	public void setProductUnitName(String productUnitName) {
		this.productUnitName = productUnitName;
	}

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	public Date getProductionDate() {
		return productionDate;
	}

	public void setProductionDate(Date productionDate) {
		this.productionDate = productionDate;
	}

	@Length(min=0, max=50, message="打印数量长度必须介于 0 和 50 之间")
	public String getPrintCount() {
		return printCount;
	}

	public void setPrintCount(String printCount) {
		this.printCount = printCount;
	}

	public Date getBeginCreateDate() {
		return beginCreateDate;
	}

	public void setBeginCreateDate(Date beginCreateDate) {
		this.beginCreateDate = beginCreateDate;
	}

	public Date getEndCreateDate() {
		return endCreateDate;
	}

	public void setEndCreateDate(Date endCreateDate) {
		this.endCreateDate = endCreateDate;
	}

	@Length(min=0, max=50, message="企业id长度必须介于 0 和 50 之间")
	public String getEntId() {
		return entId;
	}

	public void setEntId(String entId) {
		this.entId = entId;
	}

	public String getNo() {
		return no;
	}

	public void setNo(String no) {
		this.no = no;
	}

	public String getTime() {
		return time;
	}

	public void setTime(String time) {
		this.time = time;
	}

	public String getEntName() {
		return entName;
	}

	public void setEntName(String entName) {
		this.entName = entName;
	}

	public String getEntType() {
		return entType;
	}

	public void setEntType(String entType) {
		this.entType = entType;
	}

	public String getEntCode() {
		return entCode;
	}

	public void setEntCode(String entCode) {
		this.entCode = entCode;
	}

	public String getProductIntroduction() {
		return productIntroduction;
	}

	public void setProductIntroduction(String productIntroduction) {
		this.productIntroduction = productIntroduction;
	}

	public String getProductAddress() {
		return productAddress;
	}

	public void setProductAddress(String productAddress) {
		this.productAddress = productAddress;
	}

	public String getEntBusinessType() {
		return entBusinessType;
	}

	public void setEntBusinessType(String entBusinessType) {
		this.entBusinessType = entBusinessType;
	}

	public String getEntProvince() {
		return entProvince;
	}

	public void setEntProvince(String entProvince) {
		this.entProvince = entProvince;
	}

	public String getEntCity() {
		return entCity;
	}

	public void setEntCity(String entCity) {
		this.entCity = entCity;
	}

	public String getEntCounty() {
		return entCounty;
	}

	public void setEntCounty(String entCounty) {
		this.entCounty = entCounty;
	}

	public String getEntAddress() {
		return entAddress;
	}

	public void setEntAddress(String entAddress) {
		this.entAddress = entAddress;
	}

	public String getEntDetail() {
		return entDetail;
	}

	public void setEntDetail(String entDetail) {
		this.entDetail = entDetail;
	}

	public String getEntSocialCode() {
		return entSocialCode;
	}

	public void setEntSocialCode(String entSocialCode) {
		this.entSocialCode = entSocialCode;
	}

	public String getEntContactsPhone() {
		return entContactsPhone;
	}

	public void setEntContactsPhone(String entContactsPhone) {
		this.entContactsPhone = entContactsPhone;
	}

	public String getEntCompanyIntroduction() {
		return entCompanyIntroduction;
	}

	public void setEntCompanyIntroduction(String entCompanyIntroduction) {
		this.entCompanyIntroduction = entCompanyIntroduction;
	}

	public Product getProduct() {
		return product;
	}

	public void setProduct(Product product) {
		this.product = product;
	}

	public Ent getEnt() {
		return ent;
	}

	public void setEnt(Ent ent) {
		this.ent = ent;
	}

	public String getProductDetail() {
		return productDetail;
	}

	public void setProductDetail(String productDetail) {
		this.productDetail = productDetail;
	}

	public String getProductCertificationCode() {
		return productCertificationCode;
	}

	public void setProductCertificationCode(String productCertificationCode) {
		this.productCertificationCode = productCertificationCode;
	}

	public String getProductCertificationName() {
		return productCertificationName;
	}

	public void setProductCertificationName(String productCertificationName) {
		this.productCertificationName = productCertificationName;
	}

	public String getEntHonor() {
		return entHonor;
	}

	public void setEntHonor(String entHonor) {
		this.entHonor = entHonor;
	}

	public String getSortName() {
		return sortName;
	}

	public void setSortName(String sortName) {
		this.sortName = sortName;
	}

	public String getSortOrder() {
		return sortOrder;
	}

	public void setSortOrder(String sortOrder) {
		this.sortOrder = sortOrder;
	}

	public String getBlockChainId() {
		return blockChainId;
	}

	public void setBlockChainId(String blockChainId) {
		this.blockChainId = blockChainId;
	}

	public String getEntCardNo() {
		return entCardNo;
	}

	public void setEntCardNo(String entCardNo) {
		this.entCardNo = entCardNo;
	}

	public String getEntLegalPerson() {
		return entLegalPerson;
	}

	public void setEntLegalPerson(String entLegalPerson) {
		this.entLegalPerson = entLegalPerson;
	}

	public String getEntAutograph() {
		return entAutograph;
	}

	public void setEntAutograph(String entAutograph) {
		this.entAutograph = entAutograph;
	}

	public List<CertificateNo> getCertificateNoList() {
		return certificateNoList;
	}

	public void setCertificateNoList(List<CertificateNo> certificateNoList) {
		this.certificateNoList = certificateNoList;
	}

	public String getBeginSerialNumber() {
		return beginSerialNumber;
	}

	public void setBeginSerialNumber(String beginSerialNumber) {
		this.beginSerialNumber = beginSerialNumber;
	}

	public String getEndSerialNumber() {
		return endSerialNumber;
	}

	public void setEndSerialNumber(String endSerialNumber) {
		this.endSerialNumber = endSerialNumber;
	}

	public String getEntMainType() {
		return entMainType;
	}

	public void setEntMainType(String entMainType) {
		this.entMainType = entMainType;
	}

	public String getProductProvince() {
		return productProvince;
	}

	public void setProductProvince(String productProvince) {
		this.productProvince = productProvince;
	}

	public String getProductCity() {
		return productCity;
	}

	public void setProductCity(String productCity) {
		this.productCity = productCity;
	}

	public String getProductCounty() {
		return productCounty;
	}

	public void setProductCounty(String productCounty) {
		this.productCounty = productCounty;
	}

	public String getInspectionSituation() {
		return inspectionSituation;
	}

	public void setInspectionSituation(String inspectionSituation) {
		this.inspectionSituation = inspectionSituation;
	}

	public String getSampleNo() {
		return sampleNo;
	}

	public void setSampleNo(String sampleNo) {
		this.sampleNo = sampleNo;
	}

	public String getSearchType() {
		return searchType;
	}

	public void setSearchType(String searchType) {
		this.searchType = searchType;
	}

	public String getEntFarmType() {
		return entFarmType;
	}

	public void setEntFarmType(String entFarmType) {
		this.entFarmType = entFarmType;
	}

	public String getCertificateNoId() {
		return certificateNoId;
	}

	public void setCertificateNoId(String certificateNoId) {
		this.certificateNoId = certificateNoId;
	}

	public String getProductInspectionId() {
		return productInspectionId;
	}

	public void setProductInspectionId(String productInspectionId) {
		this.productInspectionId = productInspectionId;
	}

	public String getTableId() {
		return tableId;
	}

	public void setTableId(String tableId) {
		this.tableId = tableId;
	}

	public List<String> getInspectionSituationList() {
		return inspectionSituationList;
	}

	public void setInspectionSituationList(List<String> inspectionSituationList) {
		this.inspectionSituationList = inspectionSituationList;
	}

	public String getReBuyVisible() {
		return reBuyVisible;
	}

	public void setReBuyVisible(String reBuyVisible) {
		this.reBuyVisible = reBuyVisible;
	}

	public List<Attachment> getSealPicList() {
		return sealPicList;
	}

	public void setSealPicList(List<Attachment> sealPicList) {
		this.sealPicList = sealPicList;
	}

	public Autograph getAutograph() {
		return autograph;
	}

	public void setAutograph(Autograph autograph) {
		this.autograph = autograph;
	}

	public Date getSyncDate() {
		return syncDate;
	}

	public void setSyncDate(Date syncDate) {
		this.syncDate = syncDate;
	}
}
