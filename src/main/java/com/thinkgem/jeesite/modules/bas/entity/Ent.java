/**
 * Copyright &copy; 2012-2016 <a href="https://github.com/thinkgem/jeesite">JeeSite</a> All rights reserved.
 */
package com.thinkgem.jeesite.modules.bas.entity;

import java.util.Date;
import java.util.List;

import com.google.common.collect.Lists;
import com.thinkgem.jeesite.modules.sys.entity.Attachment;
import org.hibernate.validator.constraints.Length;

import com.thinkgem.jeesite.common.persistence.DataEntity;

/**
 * 企业基本信息Entity
 *
 * <AUTHOR>
 * @version 2020-06-17
 */
public class Ent extends DataEntity<Ent> {

	private static final long serialVersionUID = 1L;
	private String name; // 名称
	private String socialCode; // 统一社会信用代码
	private String legalPerson; // 法人
	private String contacts; // 联系人
	private String contactsPhone; // 联系人电话
	private String province; // 省
	private String city; // 市
	private String county; // 县区
	private String town; //镇
	private String village; // 村
	private String address; // 地址(省市县)
	private String detail; // 详细地址
	private String companyIntroduction; // 企业介绍
	private String businessType;// 主体类型(0:种植；1:养殖)
	private String entType;// 主体性质(0:企业；1:个人)
	private String mainType;// 主体类别(1:小农户、5:农产品生产企业、10:农民专业合作社、15:养殖大户、20:家庭农场)
	private String cardNo;// 身份证号
	private String cardDetail;// 身份证号
	private String lng;// 经度
	private String lat;// 纬度
	private String examineStatus;// 审核状态（0：待审核；1：通过,-1驳回）
	private String examineMan;// 审核人
	private String examineOpinion;// 审核意见
	private Date examineDate;// 审核时间
	private String tableName;// 用户表名称
	private String tableId;// 用户表ID
	private Date submitDate;// 提交时间
	private String entHonor;// 荣誉简介
	private String basicFlag;//基础信息采集对接标示(0:否 1:是)
	private String basicEnterFlag;//基础信息录入标示(0:否 1:是)
	private String farmType;//养殖分类 (0:牧业 1:渔业)
	private Integer certificateAmount;// 开具次数
	private Integer certificatePrintAmount;// 开具数量
	private Date certificatePrintDate;// 开具时间
	private Integer inspectionWriteAmount;// 上传检测报告次数(检测机构用)

	private String changeStatus;// 变更业务状态
	private String changeViewFlag;// 变更后查看标识 0:否 1:是
	private String changeOpinion;// 变更业务意见
	private String frozenFlag;// 冻结标识 0:否 1:是
	private String userId;// 登录人id
	private String dataScope;// 数据来源
	private String oldContactsPhone;// 电话号

	// 辅助字段
	private String cityName;
	private String countyName;
	private int certificatePrintCount = 0;// 打印合格证次数
	private int certificateCount = 0;// 合格证数量
	private Date startDate;// 查询开始时间
	private Date endDate;// 查询开始时间
	private String productId;// 产品ID
	private List<BasEntDetail> entDetailList;
	private boolean regionFlag;// PAD登录时该主体 是否在 当前管理员所在区域里

	private List<Attachment> fileList = Lists.newArrayList();

	private String autograph;// 签名
	private String productSortName;// 产品分类名称
	private Date beginCreateDate;	//开始注册时间
	private Date endPrintDate;		//结束注册时间
	private Date beginCertificatePrintDate;	//开始开具时间
	private Date endCertificatePrintDate;		//结束开具时间

	private String exportFlag;
	private String areaCode;		// 行政区划编码
	private String queryYear;		// 查询年份
	private String queryMonth;		// 查询月

	private Date lastModified;
	private Date syncDate;// 数据同步时间


	public String getAreaCode() {
		return areaCode;
	}

	public void setAreaCode(String areaCode) {
		this.areaCode = areaCode;
	}

	public String getQueryYear() {
		return queryYear;
	}

	public void setQueryYear(String queryYear) {
		this.queryYear = queryYear;
	}

	public String getQueryMonth() {
		return queryMonth;
	}

	public void setQueryMonth(String queryMonth) {
		this.queryMonth = queryMonth;
	}

	public String getExportFlag() {
		return exportFlag;
	}

	public void setExportFlag(String exportFlag) {
		this.exportFlag = exportFlag;
	}

	public Ent() {
		super();
	}

	public Ent(String id) {
		super(id);
	}

	@Length(min = 0, max = 50, message = "名称长度必须介于 0 和 50 之间")
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@Length(min = 0, max = 50, message = "统一社会信用代码长度必须介于 0 和 50 之间")
	public String getSocialCode() {
		return socialCode;
	}

	public void setSocialCode(String socialCode) {
		this.socialCode = socialCode;
	}

	@Length(min = 0, max = 50, message = "法人长度必须介于 0 和 50 之间")
	public String getLegalPerson() {
		return legalPerson;
	}

	public void setLegalPerson(String legalPerson) {
		this.legalPerson = legalPerson;
	}

	@Length(min = 0, max = 50, message = "联系人长度必须介于 0 和 50 之间")
	public String getContacts() {
		return contacts;
	}

	public void setContacts(String contacts) {
		this.contacts = contacts;
	}

	@Length(min = 0, max = 20, message = "联系人电话长度必须介于 0 和 20 之间")
	public String getContactsPhone() {
		return contactsPhone;
	}

	public void setContactsPhone(String contactsPhone) {
		this.contactsPhone = contactsPhone;
	}

	@Length(min = 0, max = 2, message = "省长度必须介于 0 和 2 之间")
	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	@Length(min = 0, max = 4, message = "市长度必须介于 0 和 4 之间")
	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	@Length(min = 0, max = 6, message = "县区长度必须介于 0 和 6 之间")
	public String getCounty() {
		return county;
	}

	public void setCounty(String county) {
		this.county = county;
	}

	@Length(min = 0, max = 100, message = "地址长度必须介于 0 和 100 之间")
	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	@Length(min = 0, max = 150, message = "详细地址长度必须介于 0 和 150 之间")
	public String getDetail() {
		return detail;
	}

	public void setDetail(String detail) {
		this.detail = detail;
	}

	@Length(min = 0, max = 500, message = "企业介绍长度必须介于 0 和 500 之间")
	public String getCompanyIntroduction() {
		return companyIntroduction;
	}

	public void setCompanyIntroduction(String companyIntroduction) {
		this.companyIntroduction = companyIntroduction;
	}

	@Length(min = 0, max = 1, message = "企业类型长度必须介于 0 和 1 之间")
	public String getEntType() {
		return entType;
	}

	public void setEntType(String entType) {
		this.entType = entType;
	}

	@Length(min = 0, max = 18, message = "身份证号长度必须介于 0 和 18 之间")
	public String getCardNo() {
		return cardNo;
	}

	public void setCardNo(String cardNo) {
		this.cardNo = cardNo;
	}

	public String getCardDetail() {
		return cardDetail;
	}

	public void setCardDetail(String cardDetail) {
		this.cardDetail = cardDetail;
	}

	@Length(min = 0, max = 50, message = "经度长度必须介于 0 和 50 之间")
	public String getLng() {
		return lng;
	}

	public void setLng(String lng) {
		this.lng = lng;
	}

	@Length(min = 0, max = 50, message = "纬度长度必须介于 0 和 50 之间")
	public String getLat() {
		return lat;
	}

	public void setLat(String lat) {
		this.lat = lat;
	}

	@Length(min = 0, max = 2, message = "审核状态长度必须介于 0 和 2 之间")
	public String getExamineStatus() {
		return examineStatus;
	}

	public void setExamineStatus(String examineStatus) {
		this.examineStatus = examineStatus;
	}

	@Length(min = 0, max = 50, message = "审核人长度必须介于 0 和 50 之间")
	public String getExamineMan() {
		return examineMan;
	}

	public void setExamineMan(String examineMan) {
		this.examineMan = examineMan;
	}

	@Length(min = 0, max = 200, message = "审核意见长度必须介于 0 和 200 之间")
	public String getExamineOpinion() {
		return examineOpinion;
	}

	public void setExamineOpinion(String examineOpinion) {
		this.examineOpinion = examineOpinion;
	}

	public Date getExamineDate() {
		return examineDate;
	}

	public void setExamineDate(Date examineDate) {
		this.examineDate = examineDate;
	}

	@Length(min = 0, max = 50, message = "用户表名称长度必须介于 0 和 50 之间")
	public String getTableName() {
		return tableName;
	}

	public void setTableName(String tableName) {
		this.tableName = tableName;
	}

	@Length(min = 0, max = 50, message = "用户表id长度必须介于 0 和 50 之间")
	public String getTableId() {
		return tableId;
	}

	public void setTableId(String tableId) {
		this.tableId = tableId;
	}

	public String getCityName() {
		return cityName;
	}

	public void setCityName(String cityName) {
		this.cityName = cityName;
	}

	public String getCountyName() {
		return countyName;
	}

	public void setCountyName(String countyName) {
		this.countyName = countyName;
	}

	public int getCertificatePrintCount() {
		return certificatePrintCount;
	}

	public void setCertificatePrintCount(int certificatePrintCount) {
		this.certificatePrintCount = certificatePrintCount;
	}

	public int getCertificateCount() {
		return certificateCount;
	}

	public void setCertificateCount(int certificateCount) {
		this.certificateCount = certificateCount;
	}

	public List<Attachment> getFileList() {
		return fileList;
	}

	public void setFileList(List<Attachment> fileList) {
		this.fileList = fileList;
	}

	public String getAutograph() {
		return autograph;
	}

	public void setAutograph(String autograph) {
		this.autograph = autograph;
	}

	public Date getSubmitDate() {
		return submitDate;
	}

	public void setSubmitDate(Date submitDate) {
		this.submitDate = submitDate;
	}

	@Length(min = 0, max = 1, message = "主体类型长度必须介于 0 和 1 之间")
	public String getBusinessType() {
		return businessType;
	}

	public void setBusinessType(String businessType) {
		this.businessType = businessType;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	@Length(min = 0, max = 500, message = "企业荣誉长度必须介于 0 和 500 之间")
	public String getEntHonor() {
		return entHonor;
	}

	public void setEntHonor(String entHonor) {
		this.entHonor = entHonor;
	}
	@Length(min = 0, max = 2, message = "企业介绍长度必须介于 0 和 2 之间")
	public String getMainType() {
		return mainType;
	}

	public void setMainType(String mainType) {
		this.mainType = mainType;
	}

	public String getBasicFlag() {
		return basicFlag;
	}

	public void setBasicFlag(String basicFlag) {
		this.basicFlag = basicFlag;
	}

	public String getFarmType() {
		return farmType;
	}

	public void setFarmType(String farmType) {
		this.farmType = farmType;
	}

	public String getBasicEnterFlag() {
		return basicEnterFlag;
	}

	public void setBasicEnterFlag(String basicEnterFlag) {
		this.basicEnterFlag = basicEnterFlag;
	}

	public Integer getCertificateAmount() {
		return certificateAmount;
	}

	public void setCertificateAmount(Integer certificateAmount) {
		this.certificateAmount = certificateAmount;
	}

	public Integer getCertificatePrintAmount() {
		return certificatePrintAmount;
	}

	public void setCertificatePrintAmount(Integer certificatePrintAmount) {
		this.certificatePrintAmount = certificatePrintAmount;
	}

	public String getChangeStatus() {
		return changeStatus;
	}

	public void setChangeStatus(String changeStatus) {
		this.changeStatus = changeStatus;
	}

	public String getChangeViewFlag() {
		return changeViewFlag;
	}

	public void setChangeViewFlag(String changeViewFlag) {
		this.changeViewFlag = changeViewFlag;
	}

	public String getChangeOpinion() {
		return changeOpinion;
	}

	public void setChangeOpinion(String changeOpinion) {
		this.changeOpinion = changeOpinion;
	}

	public String getFrozenFlag() {
		return frozenFlag;
	}

	public void setFrozenFlag(String frozenFlag) {
		this.frozenFlag = frozenFlag;
	}

	public String getDataScope() {
		return dataScope;
	}
	public void setDataScope(String dataScope) {
		this.dataScope = dataScope;
	}

	public String getUserId() {
		return userId;
	}
	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getProductId() {
		return productId;
	}
	public void setProductId(String productId) {
		this.productId = productId;
	}


    public List<BasEntDetail> getEntDetailList() {
        return entDetailList;
    }
    public void setEntDetailList(List<BasEntDetail> entDetailList) {
        this.entDetailList = entDetailList;
    }

    public String getOldContactsPhone() {
        return oldContactsPhone;
    }
    public void setOldContactsPhone(String oldContactsPhone) {
        this.oldContactsPhone = oldContactsPhone;
    }

	public String getTown() {
		return town;
	}

	public void setTown(String town) {
		this.town = town;
	}

	public String getVillage() {
		return village;
	}

	public void setVillage(String village) {
		this.village = village;
	}

	public boolean isRegionFlag() {
		return regionFlag;
	}

	public void setRegionFlag(boolean regionFlag) {
		this.regionFlag = regionFlag;
	}

	public Date getCertificatePrintDate() {
		return certificatePrintDate;
	}

	public void setCertificatePrintDate(Date certificatePrintDate) {
		this.certificatePrintDate = certificatePrintDate;
	}

	public String getProductSortName() {
		return productSortName;
	}

	public void setProductSortName(String productSortName) {
		this.productSortName = productSortName;
	}

	public Date getBeginCreateDate() {
		return beginCreateDate;
	}

	public void setBeginCreateDate(Date beginCreateDate) {
		this.beginCreateDate = beginCreateDate;
	}

	public Date getEndPrintDate() {
		return endPrintDate;
	}

	public void setEndPrintDate(Date endPrintDate) {
		this.endPrintDate = endPrintDate;
	}

	public Date getBeginCertificatePrintDate() {
		return beginCertificatePrintDate;
	}

	public void setBeginCertificatePrintDate(Date beginCertificatePrintDate) {
		this.beginCertificatePrintDate = beginCertificatePrintDate;
	}

	public Date getEndCertificatePrintDate() {
		return endCertificatePrintDate;
	}

	public void setEndCertificatePrintDate(Date endCertificatePrintDate) {
		this.endCertificatePrintDate = endCertificatePrintDate;
	}

	public Integer getInspectionWriteAmount() {
		return inspectionWriteAmount;
	}

	public void setInspectionWriteAmount(Integer inspectionWriteAmount) {
		this.inspectionWriteAmount = inspectionWriteAmount;
	}

	public Date getLastModified() {
		return lastModified;
	}

	public void setLastModified(Date lastModified) {
		this.lastModified = lastModified;
	}

	public Date getSyncDate() {
		return syncDate;
	}

	public void setSyncDate(Date syncDate) {
		this.syncDate = syncDate;
	}
}
