/**
 * Copyright &copy; 2012-2016 <a href="https://github.com/thinkgem/jeesite">JeeSite</a> All rights reserved.
 */
package com.thinkgem.jeesite.modules.bas.service;

import cn.hutool.core.codec.Base64;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.thinkgem.jeesite.common.config.Global;
import com.thinkgem.jeesite.common.persistence.Page;
import com.thinkgem.jeesite.common.service.CrudService;
import com.thinkgem.jeesite.common.utils.HttpClientUtil;
import com.thinkgem.jeesite.common.utils.IdGen;
import com.thinkgem.jeesite.common.utils.Sm4Util;
import com.thinkgem.jeesite.common.utils.StringUtils;
import com.thinkgem.jeesite.modules.bas.dao.ProductDao;
import com.thinkgem.jeesite.modules.bas.entity.Ent;
import com.thinkgem.jeesite.modules.bas.entity.Product;
import com.thinkgem.jeesite.modules.bas.entity.ProductStorage;
import com.thinkgem.jeesite.modules.sys.entity.Attachment;
import com.thinkgem.jeesite.modules.sys.service.AttachmentService;
import com.thinkgem.jeesite.modules.sys.utils.UserUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.text.SimpleDateFormat;

/**
 * 产品信息Service
 * <AUTHOR>
 * @version 2020-06-17
 */
@Service
@Transactional(readOnly = true)
public class ProductService extends CrudService<ProductDao, Product> {

	@Lazy
	@Autowired
	private EntService entService;

	@Autowired
	private AttachmentService attachmentService;
	
	@Autowired
	private ProductStorageService productStorageService;
	
	@Override
	public Product get(String id) {
		Product product=super.get(id);
		Attachment attachment = new Attachment();
		attachment.setTableName("bas_product");
		//获取附件信息
		attachment.setTableId(product.getId());
		List<Attachment> attachmentList = attachmentService.findList(attachment);
		for (Attachment item : attachmentList) {
			String filePath = item.getFilePath();
			String url = Global.getConfig("QIUNIU_DOMAIN") + filePath;
			item.setFileUrl(url);
		}
		product.setFileList(attachmentList);
		//获取保质信息
		product.setProductStorageList(productStorageService.findListByProductId(product.getId()));
		return product;
	}

	@Override
	public List<Product> findList(Product product) {
		return super.findList(product);
	}

	@Override
	public Page<Product> findPage(Page<Product> page, Product product) {
		return super.findPage(page, product);
	}

	/**
	 * @title: findProductPage
	 * @author: zcc
	 * @date: 2020年7月21日 上午10:16:11
	 * @description: 产品分页
	 * @param: @param product
	 * @param: @return
	 * @param: @throws Exception
	 * @return: Page
	 */
	public Page<Product> findProductPage(Page<Product> page, Product product) {
		Ent ent = new Ent();
		ent.setTableId(UserUtils.getPrincipal().getId());
		if(entService.getEnt(ent) != null){
			product.setEntId(entService.getEnt(ent).getId());
			product.setPage(page);
			return page.setList(dao.findProductPage(product));
		}else {
			return new Page<Product>();
		}
	}
	/**
	 * @title: findReBuyProductPage
	 * @author: LiuBin
	 * @date: 2024年1月16日 上午10:16:11
	 * @description: 复购产品列表分页查询
	 * @param: @param product
	 * @param: @return
	 * @param: @throws Exception
	 * @return: Page
	 */
	public Object findReBuyProductPage(Page<Product> page, Product product) throws Exception {
		String reBuyProductUrl = Global.getConfig("product.reBuyProduct.url");
		Map<String, String> requestUrlParam = new HashMap<>();

		requestUrlParam.put("pageNum", ""+page.getPageNo());
		requestUrlParam.put("pageSize",""+page.getPageSize());
		if(product == null){
			throw new Exception("缺少参数");
		}
		if(StringUtils.isEmpty(product.getShopId())){
			throw new Exception("缺少参数 shopId");
		}
		String shopIdEncry = Sm4Util.encryptEcb(product.getShopId());
		requestUrlParam.put("shopId", shopIdEncry);
		if(StringUtils.isNotEmpty(product.getName())){
			requestUrlParam.put("name", product.getName());
		}

		String codeParm =   System.currentTimeMillis()+",sxkj0818";
		String headAuthorization = Base64.encode(codeParm);
		String httpResultStr =HttpClientUtil.doGet(reBuyProductUrl,requestUrlParam,headAuthorization);
		if(StringUtils.isNotEmpty(httpResultStr)){
			JSONObject jsonObject = JSON.parseObject(httpResultStr);
			return jsonObject;
		}

		return null;
	}
	/**
	 * @title: save
	 * @author: zcc
	 * @date: 2020年7月21日 上午10:16:11
	 * @description: 产品保存
	 * @param: @param product
	 * @param: @return
	 * @param: @throws Exception
	 */
	@Override
	@Transactional(readOnly = false,rollbackFor=Exception.class)
	public void save(Product product) {
		//保存产品信息
		product.setAddDate(new Date());
		Ent ent = new Ent();
		ent.setTableId(UserUtils.getPrincipal().getId());
		product.setEntId(entService.getEnt(ent).getId());
		super.save(product);
	}

	/**
	 * @title: findCountGroupName
	 * @author: jjm
	 * @date: 2021年9月15日08:20:31
	 * @description: 保存产品
	 * @return: List<Product>
	 */
	@Transactional(readOnly = false,rollbackFor=Exception.class)
	public void saveProducts(Product product) {
		//保存产品信息
		product.setAddDate(new Date());
		product.setUserId(UserUtils.getUser().getId());
		super.save(product);
		//先删除原有附件
		Attachment attachment = new Attachment();
		attachment.setTableId(product.getId());
		attachment.setTableName("bas_product");
		attachmentService.deleteByComplaintId(attachment);

		//保存附件
		List<Attachment> finalList = product.getFileList();
		if(!finalList.isEmpty()){
			for(Attachment att : finalList){
				att.preInsert();
				att.setId(IdGen.nextId());
				att.setTableId(product.getId());
			}
			attachmentService.batchInsert(finalList);
		}
	}
	/**
	 * @title: saveProduct
	 * @author: zcc
	 * @date: 2020年7月26日 上午10:16:11
	 * @description: 产品添加方法带附件,更新也调这个方法
	 * @param: Product
	 * @return:
	 */
	@Transactional(readOnly = false,rollbackFor=Exception.class)
	public void saveProduct(Product product) {
		//保存产品信息
		this.save(product);

		//先删除原有附件
		Attachment attachment = new Attachment();
		attachment.setTableId(product.getId());
		attachment.setTableName("bas_product");
		attachmentService.deleteByComplaintId(attachment);

		//保存附件
		List<Attachment> finalList = product.getFileList();
		if(!finalList.isEmpty()){
			for(Attachment att : finalList){
				att.preInsert();
				att.setId(IdGen.nextId());
				att.setTableId(product.getId());
			}
			attachmentService.batchInsert(finalList);
		}
		
		/**
		 * 修改业务：处理删除的保质存储记录
		 * 每条记录做多3条先临时处理
		 * 2021年10月13日16:47:17
		 * lxy
		 */
		if(null!=product.getProductStorageDelIdList() && !product.getProductStorageDelIdList().isEmpty()) {
			for(String id:product.getProductStorageDelIdList()) {
				ProductStorage productStorage=productStorageService.get(id);
				productStorageService.delete(productStorage);
			}
			
		}
		/**
		 * 保质方式存储
		 * 2021年10月13日16:47:17
		 * lxy
		 */
		if(null!=product.getProductStorageList() && !product.getProductStorageList().isEmpty()) {
			for(ProductStorage item:product.getProductStorageList()) {
				item.setEntId(product.getEntId());
				item.setProductId(product.getId());
				productStorageService.save(item);
			}
		}
	}

	/**
	 * @title: findProductAttachment
	 * @author: zcc
	 * @date: 2020年7月22日 上午10:16:11
	 * @description: 获取产品图片信息
	 * @param: @param product
	 * @param: @return
	 * @param: @throws Exception
	 * @return: Product
	 */
	public Product findProductAttachment(Product product){
		product = get(product);
		Attachment attachment = new Attachment();
		attachment.setTableId(product.getId());
		attachment.setTableName("bas_product");
		List<Attachment> attachmentList = attachmentService.findList(attachment);
		for (Attachment item : attachmentList) {
			String filePath = item.getFilePath();
			//String path = Global.getConfig("filePath") + filePath;
			String url = Global.getConfig("QIUNIU_DOMAIN") + filePath;
			item.setFileUrl(url);
		}
		product.setFileList(attachmentList);
		return product;
	}

	@Override
	@Transactional(readOnly = false,rollbackFor=Exception.class)
	public void delete(Product product) {
		super.delete(product);
	}

	public List<Product> findListByEntId(String entId) {
		Product product = new Product();
		product.setEntId(entId);
		return findList(product);
	}

	/**
	 *@title: findProductList
	 *@author: CaoBochun
	 *@date: 2020年06月18日 05时57分58秒
	 *@description: 查询产品集合
	 *@param: [entId]
	 *@return: {@link List< Product>}
	 */
	public List<Product> findProductList(String entId) {
		List<Product> list= dao.findProductList(entId);
		if(null!=list && !list.isEmpty()) {
			Attachment attachment = new Attachment();
			attachment.setTableName("bas_product");
			for(Product product:list) {
				//获取附件信息
				attachment.setTableId(product.getId());
				List<Attachment> attachmentList = attachmentService.findList(attachment);
				for (Attachment item : attachmentList) {
					String filePath = item.getFilePath();
					String url = Global.getConfig("QIUNIU_DOMAIN") + filePath;
					item.setFileUrl(url);
				}
				product.setFileList(attachmentList);
				//获取保质信息
				product.setProductStorageList(productStorageService.findListByProductId(product.getId()));
			}
		}
		return list;
	}

	/**
	 * @title: deleteBatch
	 * @author: zcc
	 * @date: 2020年7月14日 上午10:16:11
	 * @description: 产品批量删除方法
	 * @param: Product
	 * @return:
	 */
	@Transactional(readOnly = false,rollbackFor=Exception.class)
	public void deleteBatch (Product product){
		dao.deleteBatch(product.getIdList());
	}

	/**
	 * 
	* @Title: updateInspectionResultById 
	* @author: LJX
	* @date: 2020年9月10日 下午3:21:41 
	* @Description: 根据id更新检测结果
	* @param:  productList
	* @return: void
	* @throws
	 */
	@Transactional(readOnly = false,rollbackFor=Exception.class)
	public void updateInspectionResultById(JSONArray productList) {
		dao.updateInspectionResultById(productList);
	}

	/**
	* @title: findInspectionList
	* @author: ZYJ
	* @date: 2020/9/10 17:15
	* @description: 获取产品信息
	* @param: [product]
	* @return: java.util.List<com.thinkgem.jeesite.modules.bas.entity.Product>
	*/
	public List<Product> findInspectionList(){
	    Ent ent = entService.getByTableId(UserUtils.getPrincipal().getId());
	    if(null == ent){
	        return new ArrayList<>();
        }
	    List<Product> list = dao.findProductList(ent.getId());
	    return list;
    }

	/**
	 *@title: getProductNameAndCertificationNameMap
	 *@author: 胡志国
	 *@date: 2021年05月07日 05时57分58秒
	 *@description: 查询企业产品名称和产品认证名称map(用于手机端app检查记录表中主要品种和产品认证显示)
	 *@param: [entId]
	 *@return: {@link Map<String, Object>}
	 */
	public Map<String, Object> getProductNameAndCertificationNameMap(String entId) {

		// 获取产品名称信息
		Map<String, String> productNameMap = new HashMap<String, String>();			// 产品名称map
		Map<String, String> certificationNameMap = new HashMap<String, String>();	// 产品认证名称map
		List<Product> productList = dao.findProductList(entId);						// 获取企业产品信息
		String productCertificationName = null;			// 产品认证名称

		for(Product product : productList) {
			productNameMap.put(product.getName(), product.getName());	// 产品名称
			productCertificationName = product.getProductCertificationName();	//获取产品认证名称
			if(productCertificationName != null && !productCertificationName.equals("")) {
				String[] certificationNames = productCertificationName.split(",");
				for(String certificationName : certificationNames) {
					certificationNameMap.put(certificationName, certificationName);
				}
			}
		}
		String productName = Joiner.on(",").join(productNameMap.values());
		String certificationName = Joiner.on(",").join(certificationNameMap.values());
		Map<String, Object> map = new HashMap<>();
		map.put("productName", productName);
		map.put("certificationName", certificationName);
		return map;
	}

	/**
	 * @title: findCountGroupName
	 * @author: jjm
	 * @date: 2021年9月15日08:20:31
	 * @description: 分页查询
	 * @return: List<Product>
	 */
	public Page<Product> findProductAllList(Page<Product> page, Product product) {
		product.setPage(page);
		return page.setList(dao.findProductAllList(product));
	}

	/**
	 * @title: findCountGroupName
	 * @author: jjm
	 * @date: 2021年9月15日08:20:31
	 * @description: 根据产品数量
	 * @return: Map<String,Object>
	 */
	public Map<String,Object> findCountGroupName(Product entity){
		entity.setUserId(UserUtils.getUser().getId());
		return  dao.findCountGroupName(entity);
	}
	/**
	 * @title: findProductById
	 * @author: jjm
	 * @date: 2021年9月15日08:20:31
	 * @description: 根据id查询所有
	 * @return: Product
	 */
	public Product findProductById(String id){
		Attachment attachment = new Attachment();
		attachment.setTableId(id);
		attachment.setTableName("bas_product");
		return dao.findProductById(id);
	}
	/**
	 * @title: updateById
	 * @author: jjm
	 * @date: 2021年9月15日08:20:31
	 * @description: 根据id修改
	 * @return: Integer
	 */
	@Transactional(readOnly = false)
	public Integer updateById(Product product){
		//先删除原有附件
		Attachment attachment = new Attachment();
		attachment.setTableId(product.getId());
		attachment.setTableName("bas_product");
		attachmentService.deleteByComplaintId(attachment);
		//保存附件
		List<Attachment> finalList = product.getFileList();
		if(!finalList.isEmpty()){
			for(Attachment att : finalList){
				att.preInsert();
				att.setId(IdGen.nextId());
				att.setTableId(product.getId());
			}
			attachmentService.batchInsert(finalList);
		}
		return dao.updateById(product);
	};
	/**
	 * @title: deleteFlag
	 * @author: jjm
	 * @date: 2021年9月15日08:20:31
	 * @description: 删除
	 * @return: Integer
	 */
	@Transactional(readOnly = false)
	public Integer deleteFlag(String id){
		return dao.deleteFlag(id);
	};
	/**
	 * @title: findProductCount
	 * @author: jjm
	 * @date: 2021年9月15日08:20:31
	 * @description: 根据名字获取数量
	 * @return: List<Map<String,Object>>
	 */
	public Map<String,Object> findProductCount(String name){
		return dao.findProductCount(name,UserUtils.getUser().getId());
	};


    /**
     * 根据user_id查询 分两种情况 1是有开证记录的产品排在前面 2是没有开证则按照add_Date
     *
     * <AUTHOR>
     * @date 2021/9/14 9:02
     * @param product 产品对象
     * @return java.util.List<com.thinkgem.jeesite.modules.bas.entity.Product>
     */
    public List<Product> findListByUserId(Product product) {
        return dao.findListByUserId(product);
    }
    /**
	 * 
	* @title: updatePrintAmount
	* @author: lxy
	* @date: 2022年1月10日14:25:28
	* @description: 更新打印数量 
	* @param: id
	* @param: amount
	* @return: int
	 */
	public Integer updatePrintAmount(String id,Integer amount) {
		if(StringUtils.isBlank(id) || null == amount) {
			return null;
		}
		return dao.updatePrintAmount(id, amount);
	}
	
	/**
     * 
    * @Title: findListPage 
    * @author: lxy
    * @date: 2022年7月15日11:24:46 
    * @Description: 产品集合
    * @param:  product 
    * @return: Page<Product>
     */
	public Page<Product> findListPage(Page<Product> page, Product product) {
		product.setPage(page);
		page.setList(dao.findListPage(product));
		return page;
	}


	@Transactional(readOnly = false,rollbackFor=Exception.class)
	public Integer updateValidPrintAmount(String productId,String entId) {
		if(StringUtils.isBlank(productId) || null == productId) {
			return null;
		}
		if(StringUtils.isBlank(entId) || null == entId) {
			return null;
		}

		return dao.updateInvalidPrintAmount(productId, entId);
	}

	public boolean checkPhoneNumOpenShopCart(String phoneNum) throws Exception {
		if(StringUtils.isEmpty(phoneNum)){
			throw new Exception("缺少参数 phoneNum");
		}
		String phoneNumEncrypt = Sm4Util.encryptEcb(phoneNum);
		String phoneCheckUrl= Global.getConfig("source.checkPhoneNum.url");
		phoneCheckUrl+="?providerNumber="+phoneNumEncrypt;
		String httpResultStr = HttpClientUtil.doGet(phoneCheckUrl);
		if(StringUtils.isNotEmpty(httpResultStr)){
			JSONObject jsonObject = JSON.parseObject(httpResultStr);
			if(jsonObject!=null){
				String code = jsonObject.getString("code");
				if("200".equals(code)){
					boolean data = jsonObject.getBoolean("data");
					if(data){
						return true;
					}
				}
			}

		}
		return false;
	}

	/**
	 * 获取Product表中syncDate字段的最大值
	 * @return 最大的syncDate，如果没有记录则返回null
	 */
	public Date getMaxSyncDate() {
		return dao.getMaxSyncDate();
	}

	/**
	 * 批量保存或更新Product数据（用于同步）
	 * @param productList 产品列表
	 * @param currentSyncTime 当前同步时间
	 * @return 处理成功的数量
	 */
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int batchSaveOrUpdateForSync(List<Product> productList, Date currentSyncTime) {
		if (productList == null || productList.isEmpty()) {
			return 0;
		}

		// 为每个产品设置同步时间和其他必要字段
		for (Product product : productList) {
			product.setSyncDate(currentSyncTime);

			// 如果是新记录，设置创建信息
			if (StringUtils.isBlank(product.getId())) {
				product.setId(IdGen.nextId());
				product.preInsert();
			} else {
				// 如果是更新记录，设置更新信息
				product.preUpdate();
			}
		}

		return dao.batchInsertOrUpdate(productList);
	}

	/**
	 * 转换删除标识
	 * 外部接口规则：delFlag=1表示数据可用，非1表示数据不可用
	 * 本项目规则：delFlag=0表示正常，delFlag=1表示删除
	 * @param externalDelFlag 外部系统的删除标识
	 * @return 本系统的删除标识
	 */
	public String convertDelFlag(String externalDelFlag) {
		if ("1".equals(externalDelFlag)) {
			return "0"; // 外部delFlag=1 → 本地delFlag=0（正常）
		} else {
			return "1"; // 外部delFlag≠1 → 本地delFlag=1（删除）
		}
	}


}