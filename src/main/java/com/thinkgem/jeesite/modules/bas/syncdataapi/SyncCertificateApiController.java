package com.thinkgem.jeesite.modules.bas.syncdataapi;

import com.thinkgem.jeesite.common.persistence.Page;
import com.thinkgem.jeesite.common.persistence.ResponseResult;
import com.thinkgem.jeesite.common.web.BaseController;
import com.thinkgem.jeesite.modules.bas.entity.Certificate;
import com.thinkgem.jeesite.modules.bas.entity.Product;
import com.thinkgem.jeesite.modules.bas.service.CertificateService;
import com.thinkgem.jeesite.modules.bas.service.ProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "${syncApiPath}/certificate")
public class SyncCertificateApiController extends BaseController{

	@Autowired
	private CertificateService certificateService;

	@RequestMapping(value = "list", method = RequestMethod.POST)
	public ResponseResult list(@RequestBody Certificate certificate){
		return ResponseResult.success(certificateService.findList(certificate));
	}

	@RequestMapping(value = "page", method = RequestMethod.POST)
	public ResponseResult page(@RequestBody Certificate certificate){
		return ResponseResult.success(certificateService.findCertificatePage(new Page<Certificate>(), certificate));
	}

	@RequestMapping(value = "count", method = RequestMethod.POST)
	public ResponseResult count(@RequestBody Certificate certificate){
		return ResponseResult.success(certificateService.selectCertificateCount(certificate));
	}

}
