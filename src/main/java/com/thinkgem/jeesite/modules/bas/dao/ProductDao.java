/**
 * Copyright &copy; 2012-2016 <a href="https://github.com/thinkgem/jeesite">JeeSite</a> All rights reserved.
 */
package com.thinkgem.jeesite.modules.bas.dao;

import com.alibaba.fastjson.JSONArray;
import com.thinkgem.jeesite.common.persistence.CrudDao;
import com.thinkgem.jeesite.common.persistence.annotation.MyBatisDao;
import com.thinkgem.jeesite.modules.bas.entity.Product;

import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 产品信息DAO接口
 * <AUTHOR>
 * @version 2020-06-17
 */
@MyBatisDao
public interface ProductDao extends CrudDao<Product> {

    /**
     *@title: findProductList
     *@author: <PERSON><PERSON><PERSON><PERSON>
     *@date: 2020年06月18日 05时58分17秒
     *@description: 查询产品集合
     *@param: [entId]
     *@return: {@link List< Product>}
     */
    List<Product> findProductList(@Param("entId") String entId);

    List<Product> findProductPage(Product product);

    int deleteBatch (List<String> list);

    /**
     * 
    * @Title: updateInspectionResultById 
    * @author: LJX
    * @date: 2020年9月10日 下午3:27:22 
    * @Description: 根据id更新检测结果
    * @param:  productList 
    * @return: void
    * @throws
     */
	void updateInspectionResultById(JSONArray productList);
    /**
     * @title: findCountGroupName
     * @author: jjm
     * @date: 2021年9月15日08:20:31
     * @description: 分页查询
     * @return: List<Product>
     */
    List<Product> findProductAllList(Product entity);

    /**
     * @title: findCountGroupName
     * @author: jjm
     * @date: 2021年9月15日08:20:31
     * @description: 根据产品数量
     * @return: Map<String,Object>
     */
    Map<String,Object> findCountGroupName(Product entity);

    /**
     * @title: findProductById
     * @author: jjm
     * @date: 2021年9月15日08:20:31
     * @description: 根据id查询所有
     * @return: Product
     */
    Product findProductById(String id);
    /**
     * @title: updateById
     * @author: jjm
     * @date: 2021年9月15日08:20:31
     * @description: 根据id修改
     * @return: Integer
     */
    Integer updateById(Product product);
    /**
     * @title: deleteFlag
     * @author: jjm
     * @date: 2021年9月15日08:20:31
     * @description: 删除
     * @return: Integer
     */
    Integer deleteFlag(String id);

    /**
     * @title: findProductCount
     * @author: jjm
     * @date: 2021年9月15日08:20:31
     * @description: 根据名字获取数量
     * @return: List<Map<String,Object>>
     */
    Map<String,Object> findProductCount(@Param("name") String name,@Param("userId") String userId);

    /**
     * 根据user_id查询 分两种情况 1是有开证记录的产品排在前面 2是没有开证则按照add_Date
     *
     * <AUTHOR>
     * @date 2021/9/14 9:02
     * @param entity 产品对象
     * @return java.util.List<com.thinkgem.jeesite.modules.bas.entity.Product>
     */
    List<Product> findListByUserId(Product entity);
    
    /**
	 * 
	* @title: updatePrintAmount
	* @author: lxy
	* @date: 2022年1月10日14:25:28
	* @description: 更新打印数量 
	* @param: id
	* @param: amount
	* @return: int
	 */
	public int updatePrintAmount(@Param("id") String id,@Param("amount") Integer amount);
	
	/**
     * 
    * @Title: findListPage 
    * @author: lxy
    * @date: 2022年7月15日11:24:46 
    * @Description: 产品集合
    * @param:  product 
    * @return: List<Product>
     */
	List<Product> findListPage(Product product);

    public int updateInvalidPrintAmount(@Param("productId") String productId,@Param("entId") String entId);

    /**
     * 查询Product表中syncDate字段的最大值
     * @return 最大的syncDate，如果没有记录则返回null
     */
    Date getMaxSyncDate();

    /**
     * 批量插入或更新Product数据
     * @param productList 产品列表
     * @return 影响的行数
     */
    int batchInsertOrUpdate(@Param("list") List<Product> productList);

    /**
     * 批量查询Product ID是否存在
     * @param idList ID列表
     * @return 存在的ID列表
     */
    List<String> findExistingIds(@Param("idList") List<String> idList);
}