/**
 * Copyright &copy; 2012-2016 <a href="https://github.com/thinkgem/jeesite">JeeSite</a> All rights reserved.
 */
package com.thinkgem.jeesite.modules.bas.dao;

import java.util.List;
import java.util.Map;

import com.thinkgem.jeesite.common.persistence.CrudDao;
import com.thinkgem.jeesite.common.persistence.annotation.MyBatisDao;
import com.thinkgem.jeesite.modules.bas.entity.Certificate;
import com.thinkgem.jeesite.modules.bas.entity.Ent;
import org.apache.ibatis.annotations.Param;

/**
 * 合格证信息DAO接口
 * <AUTHOR>
 * @version 2020-06-17
 */
@MyBatisDao
public interface CertificateDao extends CrudDao<Certificate> {

    /**
     *@title: searchNextNo
     *@author: <PERSON><PERSON><PERSON><PERSON>
     *@date: 2020年06月18日 06时02分39秒
     *@description: 查询最大编号的下一个
     *@param: []
     *@return: {@link Integer}
     */
    Integer searchNextNo(@Param(value = "code") String code);

    /**
     *
    * @Title: getFrequencyByEntId
    * @author: LJX
    * @date: 2020年7月19日 下午2:57:37
    * @Description: 根据企业ID获取打印合格证次数和数量
    * @param:  ent
    * @return: Map<String,Object>
    * @throws
     */
	Map<String, Object> getFrequencyByEntId(Ent ent);

	/**
	 *
	* @Title: getFrequencyByCountyCode
	* @author: LJX
	* @date: 2020年7月19日 下午6:08:00
	* @Description:根据企业县区code获取打印合格证次数和数量
	* @param:  county （Certificate certificate 胡志国修改 20210608）
	* @return: Map<String,Object>
	* @throws
	 */
	Map<String, Object> getFrequencyByCountyCode(Certificate certificate);

	/**
	 * @Title insertAll
	 * @Description 插入合格证信息并冗余企业、产品信息
	 * <AUTHOR>
	 * @Date 2020/7/21 16:59
	 * @Param Certificate
	 * @Return int
	 **/
	int insertAll(Certificate Certificate);

	/**
	 * @Title updatePrintCount
	 * @Description 更新打印数量
	 * <AUTHOR>
	 * @Date 2020/7/21 16:58
	 * @Param entity
	 * @Return int
	 **/
	int updatePrintCount(Certificate Certificate);

	/**
	 *
	* @Title: getSummaryInfo
	* @author: lxy
	* @date: 2020年7月19日 下午6:08:00
	* @Description:获取汇总信息
	* @param:  county
	* @return: Map<String,Object>
	* @throws
	 */
	public Map<String, Object> getSummaryInfo(@Param(value = "entId") String entId);

	/**
	 * @Title updateBlockchainId
	 * @Description //TODO 为合格证绑定区块链id
	 * <AUTHOR>
	 * @Date 2020/7/26 16:25
	 * @Param id
	 * @Param blockChainId
	 * @Return int
	 **/
	int updateBlockChainId(@Param(value = "id")String id,@Param(value = "blockChainId")String blockChainId);

	/**
	 *
	* @title: findInvalidList
	* @author: lxy
	* @date: 2021年5月13日 上午11:24:11
	* @description: 查询作废数据列表
	* @param: Certificate
	* @return: List<Certificate>
	 */
	public List<Certificate> findInvalidList(Certificate Certificate);

	/**
	 * @title:findPrintTotalNum
	 * @author:Lizhongyao
	 * @data:2021年09月11日 11:22:28
	 * @param:certificate
	 * @description 获取开具数量
	 * @return java.lang.Integer
	 **/
	Integer findPrintTotalNum(Certificate certificate);

	public int selectCertificateCount(Certificate certificate);
}
