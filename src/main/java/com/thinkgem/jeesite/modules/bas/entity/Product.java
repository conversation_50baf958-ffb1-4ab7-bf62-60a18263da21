/**
 * Copyright &copy; 2012-2016 <a href="https://github.com/thinkgem/jeesite">JeeSite</a> All rights reserved.
 */
package com.thinkgem.jeesite.modules.bas.entity;

import com.google.common.collect.Lists;
import org.hibernate.validator.constraints.Length;
import com.thinkgem.jeesite.modules.sys.entity.Attachment;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import com.thinkgem.jeesite.common.persistence.DataEntity;

/**
 * 产品信息Entity
 * <AUTHOR>
 * @version 2020-06-17
 */
public class Product extends DataEntity<Product> {

	private static final long serialVersionUID = 1L;
	private String name;		// 名称
	private String entId;		// 企业id
	private String productSortCode;		// 分类code
	private String productSortName;		// 分类名称
	private String productCertificationCode; // 产品认证（有机农产品，绿色食品，无公害农产品，农产品地理标志）多选
	private String productCertificationName; // 产品认证名称
	private Date addDate;		// 添加时间
	private String province;		// 省
	private String city;		// 市
	private String county;		// 县区
	private String address;		// 地址（省市县）
	private String detail;		// 详细地址
	private String productIntroduction;		// 产品介绍
	private Date beginAddDate;		// 开始 添加时间
	private Date endAddDate;		// 结束 添加时间
	private String longitude; //经度
	private String latitude; //纬度
	private String addDateOrder;
	private String printCountOrder;
	private String count; //打印数量
	private String currentSampleNo; //最新样品编号
	private String queryCodeUrl; //二维码url
	private String userId; //用户id（乡村版合格证用）
	private String dataScope; //数据来源
	private Integer printAmount; //开具数量
	private Date printDate;		// 开具时间

	private Date beginPrintDate;		// 开始 开具时间
	private Date endPrintDate;		// 结束 开具时间

	/** 2021年12月29日15:10:26 新增业务属性 lxy start **/
	private String electricityLink; //电商链接
	private String scaleAmount; //二维码种养殖规模
	private String scaleUnitCode; //规模单位code
	private String scaleUnitName; //规模单位name
	private String productionValue; //年产值
	private String productionAmount; //年产量
	private String productionUnitCode; //年产量单位code
	private String productionUnitName; //年产量单位name
	/** 2021年12月29日15:10:26 新增业务属性 lxy end **/

	private String reBuyProductId; //复购商品id
	private String reBuyVisible; //复购按钮是否显示1显示,其他不显示

	private String shopId;//电话号密文(查询商品列表参数)
	private String reBuyProductName;//复购商品名称

	private String saveType;// 用来区分是不是sql插入的

	private Date syncDate;

	public String getSaveType() {
		return saveType;
	}

	public void setSaveType(String saveType) {
		this.saveType = saveType;
	}

	private List<ProductStorage> productStorageList;
	private List<String> productStorageDelIdList;
	private List<Attachment> fileList = Lists.newArrayList();
	private List<String> idList = Lists.newArrayList();

	public Product() {
		super();
	}

	public Product(String id){
		super(id);
	}

	@Length(min=0, max=50, message="名称长度必须介于 0 和 50 之间")
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@Length(min=0, max=50, message="企业id长度必须介于 0 和 50 之间")
	public String getEntId() {
		return entId;
	}

	public void setEntId(String entId) {
		this.entId = entId;
	}

	@Length(min=0, max=50, message="分类code长度必须介于 0 和 50 之间")
	public String getProductSortCode() {
		return productSortCode;
	}

	public void setProductSortCode(String productSortCode) {
		this.productSortCode = productSortCode;
	}

	@Length(min=0, max=50, message="分类名称长度必须介于 0 和 50 之间")
	public String getProductSortName() {
		return productSortName;
	}

	public void setProductSortName(String productSortName) {
		this.productSortName = productSortName;
	}

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	public Date getAddDate() {
		return addDate;
	}

	public void setAddDate(Date addDate) {
		this.addDate = addDate;
	}

	@Length(min=0, max=2, message="省长度必须介于 0 和 2 之间")
	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	@Length(min=0, max=4, message="市长度必须介于 0 和 4 之间")
	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	@Length(min=0, max=6, message="县区长度必须介于 0 和 6 之间")
	public String getCounty() {
		return county;
	}

	public void setCounty(String county) {
		this.county = county;
	}

	@Length(min=0, max=100, message="地址长度必须介于 0 和 100 之间")
	public String getAddress() {
		return address;
	}


	public void setAddress(String address) {
		this.address = address;
	}
	@Length(min=0, max=100, message="详细地址长度必须介于 0 和 100 之间")
	public String getDetail() {
		return detail;
	}

	public void setDetail(String detail) {
		this.detail = detail;
	}

	@Length(min=0, max=500, message="产品介绍长度必须介于 0 和 500 之间")
	public String getProductIntroduction() {
		return productIntroduction;
	}

	public void setProductIntroduction(String productIntroduction) {
		this.productIntroduction = productIntroduction;
	}

	public Date getBeginAddDate() {
		return beginAddDate;
	}

	public void setBeginAddDate(Date beginAddDate) {
		this.beginAddDate = beginAddDate;
	}

	public Date getEndAddDate() {
		return endAddDate;
	}

	public void setEndAddDate(Date endAddDate) {
		this.endAddDate = endAddDate;
	}

	@Length(min = 0, max = 50, message = "经度长度必须介于 0 和 50 之间")
	public String getLongitude() {
		return longitude;
	}

	public void setLongitude(String longitude) {
		this.longitude = longitude;
	}

	@Length(min = 0, max = 50, message = "纬度长度必须介于 0 和 50 之间")
	public String getLatitude() {
		return latitude;
	}

	public void setLatitude(String latitude) {
		this.latitude = latitude;
	}

	public String getCount() {
		return count;
	}

	public void setCount(String count) {
		this.count = count;
	}

	public List<Attachment> getFileList() {
		return fileList;
	}

	public void setFileList(List<Attachment> fileList) {
		this.fileList = fileList;
	}

	public String getAddDateOrder() {
		return addDateOrder;
	}

	public void setAddDateOrder(String addDateOrder) {
		this.addDateOrder = addDateOrder;
	}

	public String getPrintCountOrder() {
		return printCountOrder;
	}

	public void setPrintCountOrder(String printCountOrder) {
		this.printCountOrder = printCountOrder;
	}

	@Length(min=0, max=50, message="产品认证code长度必须介于 0 和 50 之间")
	public String getProductCertificationCode() {
		return productCertificationCode;
	}

	public void setProductCertificationCode(String productCertificationCode) {
		this.productCertificationCode = productCertificationCode;
	}
	@Length(min=0, max=50, message="产品认证名称长度必须介于 0 和 50 之间")
	public String getProductCertificationName() {
		return productCertificationName;
	}

	public void setProductCertificationName(String productCertificationName) {
		this.productCertificationName = productCertificationName;
	}

	public List<String> getIdList() {
		return idList;
	}

	public void setIdList(List<String> idList) {
		this.idList = idList;
	}

	public String getCurrentSampleNo() {
		return currentSampleNo;
	}

	public void setCurrentSampleNo(String currentSampleNo) {
		this.currentSampleNo = currentSampleNo;
	}

    public String getQueryCodeUrl() {
        return queryCodeUrl;
    }

    public void setQueryCodeUrl(String queryCodeUrl) {
        this.queryCodeUrl = queryCodeUrl;
    }

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getDataScope() {
		return dataScope;
	}

	public void setDataScope(String dataScope) {
		this.dataScope = dataScope;
	}

	public List<ProductStorage> getProductStorageList() {
		return productStorageList;
	}

	public void setProductStorageList(List<ProductStorage> productStorageList) {
		this.productStorageList = productStorageList;
	}

	public List<String> getProductStorageDelIdList() {
		return productStorageDelIdList;
	}

	public void setProductStorageDelIdList(List<String> productStorageDelIdList) {
		this.productStorageDelIdList = productStorageDelIdList;
	}

	public String getElectricityLink() {
		return electricityLink;
	}

	public void setElectricityLink(String electricityLink) {
		this.electricityLink = electricityLink;
	}

	public String getScaleAmount() {
		return scaleAmount;
	}

	public void setScaleAmount(String scaleAmount) {
		this.scaleAmount = scaleAmount;
	}

	public String getScaleUnitCode() {
		return scaleUnitCode;
	}

	public void setScaleUnitCode(String scaleUnitCode) {
		this.scaleUnitCode = scaleUnitCode;
	}

	public String getScaleUnitName() {
		return scaleUnitName;
	}

	public void setScaleUnitName(String scaleUnitName) {
		this.scaleUnitName = scaleUnitName;
	}

	public String getProductionValue() {
		return productionValue;
	}

	public void setProductionValue(String productionValue) {
		this.productionValue = productionValue;
	}

	public String getProductionAmount() {
		return productionAmount;
	}

	public void setProductionAmount(String productionAmount) {
		this.productionAmount = productionAmount;
	}

	public String getProductionUnitCode() {
		return productionUnitCode;
	}

	public void setProductionUnitCode(String productionUnitCode) {
		this.productionUnitCode = productionUnitCode;
	}

	public String getProductionUnitName() {
		return productionUnitName;
	}

	public void setProductionUnitName(String productionUnitName) {
		this.productionUnitName = productionUnitName;
	}

	public Integer getPrintAmount() {
		return printAmount;
	}

	public void setPrintAmount(Integer printAmount) {
		this.printAmount = printAmount;
	}

	public Date getPrintDate() {
		return printDate;
	}

	public void setPrintDate(Date printDate) {
		this.printDate = printDate;
	}

	public Date getBeginPrintDate() {
		return beginPrintDate;
	}

	public void setBeginPrintDate(Date beginPrintDate) {
		this.beginPrintDate = beginPrintDate;
	}

	public Date getEndPrintDate() {
		return endPrintDate;
	}

	public void setEndPrintDate(Date endPrintDate) {
		this.endPrintDate = endPrintDate;
	}

	public String getReBuyProductId() {
		return reBuyProductId;
	}

	public void setReBuyProductId(String reBuyProductId) {
		this.reBuyProductId = reBuyProductId;
	}

	public String getReBuyVisible() {
		return reBuyVisible;
	}

	public void setReBuyVisible(String reBuyVisible) {
		this.reBuyVisible = reBuyVisible;
	}

	public String getShopId() {
		return shopId;
	}

	public void setShopId(String shopId) {
		this.shopId = shopId;
	}

	public String getReBuyProductName() {
		return reBuyProductName;
	}

	public void setReBuyProductName(String reBuyProductName) {
		this.reBuyProductName = reBuyProductName;
	}

	public Date getSyncDate() {
		return syncDate;
	}

	public void setSyncDate(Date syncDate) {
		this.syncDate = syncDate;
	}
}
